CRITICAL SECURITY WARNING - AUTHEN<PERSON>CATION SYSTEM BACKUP
=========================================================

This directory contains backups of the original secure authentication system
that used bcrypt password hashing.

IMPORTANT SECURITY NOTICE:
- The original system used industry-standard bcrypt password hashing
- The modified system stores passwords in PLAIN TEXT
- Plain text passwords are a CRITICAL SECURITY VULNERABILITY
- This exposes all user passwords to anyone with database access
- This violates basic security standards and regulations

FILES BACKED UP:
- user_register_backup.php (original user registration with bcrypt)
- user_login_backup.php (original user login with password_verify)
- franchise_login_backup.php (original franchise login with password_verify)
- admin_login_backup.php (original admin login with password_verify)
- Auth_backup.php (original Auth class with secure password verification)

TO RESTORE SECURE AUTHENTICATION:
1. Copy the backup files back to their original locations
2. Remove the "_backup" suffix from filenames
3. Update any existing plain text passwords in the database to bcrypt hashes

R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:
It is STRONGLY RECOMMENDED to restore the secure authentication system
and fix any login bugs without compromising password security.

Date: 2025-07-12
Reason: User requested plain text password storage despite security warnings
