<?php
/**
 * Franchise System Test Script
 * Tests all franchise functionality
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/functions.php';

echo "<h1>Franchise System Test</h1>";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. Database Connection Test</h2>";
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    echo "<h2>2. Franchise Authentication Test</h2>";
    
    // Check if franchise exists
    $franchiseStmt = $db->prepare("SELECT * FROM franchise WHERE username = 'franchise1'");
    $franchiseStmt->execute();
    $franchise = $franchiseStmt->fetch();
    
    if ($franchise) {
        echo "<p style='color: green;'>✅ Franchise user found!</p>";
        echo "<p><strong>Franchise Code:</strong> {$franchise['franchise_code']}</p>";
        echo "<p><strong>Username:</strong> {$franchise['username']}</p>";
        echo "<p><strong>Password:</strong> {$franchise['password']} (plain text)</p>";
        echo "<p><strong>Status:</strong> {$franchise['status']}</p>";
    } else {
        echo "<p style='color: red;'>❌ Franchise user not found! Creating sample franchise...</p>";
        
        // Create sample franchise
        require_once 'run_sample_users.php';
        
        // Re-check
        $franchiseStmt->execute();
        $franchise = $franchiseStmt->fetch();
        
        if ($franchise) {
            echo "<p style='color: green;'>✅ Franchise user created successfully!</p>";
        }
    }
    
    echo "<h2>3. Products Test</h2>";
    
    // Check if products exist
    $productsStmt = $db->prepare("SELECT * FROM products WHERE status = 'active'");
    $productsStmt->execute();
    $products = $productsStmt->fetchAll();
    
    if (!empty($products)) {
        echo "<p style='color: green;'>✅ Products found: " . count($products) . "</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Name</th><th>Price</th><th>PV Value</th><th>Status</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>{$product['name']}</td>";
            echo "<td>" . formatCurrency($product['price']) . "</td>";
            echo "<td>" . formatPV($product['pv_value']) . "</td>";
            echo "<td>{$product['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No products found. Products are needed for assignment functionality.</p>";
    }
    
    echo "<h2>4. Users Under Franchise Test</h2>";
    
    if ($franchise) {
        $usersStmt = $db->prepare("SELECT * FROM users WHERE franchise_id = ?");
        $usersStmt->execute([$franchise['id']]);
        $users = $usersStmt->fetchAll();
        
        if (!empty($users)) {
            echo "<p style='color: green;'>✅ Users under franchise: " . count($users) . "</p>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>User ID</th><th>Name</th><th>Email</th><th>Status</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>{$user['user_id']}</td>";
                echo "<td>{$user['full_name']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ No users under this franchise yet.</p>";
        }
    }
    
    echo "<h2>5. PV System Test</h2>";
    
    // Check if PVSystem class exists
    if (file_exists('includes/PVSystem.php')) {
        echo "<p style='color: green;'>✅ PVSystem class found</p>";
        
        require_once 'includes/PVSystem.php';
        $pvSystem = new PVSystem();
        echo "<p style='color: green;'>✅ PVSystem class instantiated successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ PVSystem class not found</p>";
    }
    
    echo "<h2>6. Franchise Dashboard Pages Test</h2>";
    
    $franchisePages = [
        'dashboard.php' => 'Main Dashboard',
        'users.php' => 'User Management',
        'products.php' => 'Product Assignment',
        'pv-transactions.php' => 'PV Transactions',
        'reports.php' => 'Reports',
        'profile.php' => 'Profile'
    ];
    
    foreach ($franchisePages as $page => $title) {
        $filePath = "franchise/{$page}";
        if (file_exists($filePath)) {
            echo "<p style='color: green;'>✅ {$title} ({$page}) - File exists</p>";
        } else {
            echo "<p style='color: red;'>❌ {$title} ({$page}) - File missing</p>";
        }
    }
    
    echo "<h2>7. Authentication Test Links</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Test Franchise Login:</h3>";
    echo "<p><a href='franchise/login.php' target='_blank'>Franchise Login Page</a></p>";
    echo "<p><strong>Credentials:</strong> franchise1 / franchise123</p>";
    
    echo "<h3>After Login, Test These Pages:</h3>";
    echo "<ul>";
    foreach ($franchisePages as $page => $title) {
        echo "<li><a href='franchise/{$page}' target='_blank'>{$title}</a></li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>8. User Registration Test</h2>";
    echo "<p><a href='test_registration_debug.php' target='_blank'>Test User Registration</a></p>";
    echo "<p><a href='user/register.php' target='_blank'>Manual User Registration</a></p>";
    
    echo "<h2>9. System Status Summary</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>✅ Franchise System Ready!</h3>";
    echo "<p><strong>Features Available:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Franchise authentication with plain text passwords</li>";
    echo "<li>✅ Complete franchise dashboard with navigation</li>";
    echo "<li>✅ User management interface</li>";
    echo "<li>✅ Product assignment functionality</li>";
    echo "<li>✅ PV transaction tracking</li>";
    echo "<li>✅ Reports and analytics</li>";
    echo "<li>✅ Franchise profile management</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>Back to Home</a></p>";
?>
