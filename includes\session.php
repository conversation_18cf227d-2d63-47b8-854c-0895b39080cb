<?php
/**
 * Session Management
 * MLM Binary Plan System
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Session timeout check
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
    session_unset();
    session_destroy();
    header("Location: login.php?timeout=1");
    exit();
}

// Update last activity time
$_SESSION['last_activity'] = time();

/**
 * Set user session
 */
function setUserSession($userType, $userId, $userData = []) {
    $_SESSION['user_type'] = $userType;
    $_SESSION['user_id'] = $userId;
    $_SESSION['user_data'] = $userData;
    $_SESSION['login_time'] = time();
    $_SESSION['last_activity'] = time();
    
    // Regenerate session ID for security
    session_regenerate_id(true);
}

/**
 * Destroy user session
 */
function destroyUserSession() {
    session_unset();
    session_destroy();
}

/**
 * Get current user data
 */
function getCurrentUser() {
    return $_SESSION['user_data'] ?? null;
}

/**
 * Get current user ID
 */
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get current user type
 */
function getCurrentUserType() {
    return $_SESSION['user_type'] ?? null;
}

/**
 * Check if current user is admin
 */
function isAdmin() {
    return getCurrentUserType() === 'admin';
}

/**
 * Check if current user is franchise
 */
function isFranchise() {
    return getCurrentUserType() === 'franchise';
}

/**
 * Check if current user is regular user
 */
function isUser() {
    return getCurrentUserType() === 'user';
}

/**
 * Set flash message
 */
function setFlashMessage($type, $message) {
    $_SESSION['flash_messages'][] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Get and clear flash messages
 */
function getFlashMessages() {
    $messages = $_SESSION['flash_messages'] ?? [];
    unset($_SESSION['flash_messages']);
    return $messages;
}

/**
 * Set success message
 */
function setSuccessMessage($message) {
    setFlashMessage('success', $message);
}

/**
 * Set error message
 */
function setErrorMessage($message) {
    setFlashMessage('error', $message);
}

/**
 * Set warning message
 */
function setWarningMessage($message) {
    setFlashMessage('warning', $message);
}

/**
 * Set info message
 */
function setInfoMessage($message) {
    setFlashMessage('info', $message);
}
?>
