<?php
/**
 * Debug User Registration Issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/functions.php';
require_once 'includes/Validator.php';
require_once 'includes/BinaryTree.php';

echo "<h1>User Registration Debug</h1>";

try {
    $db = Database::getInstance();
    
    // Check if we have sample users to use as sponsors
    echo "<h2>Available Sponsors</h2>";
    $stmt = $db->prepare("SELECT user_id, username, full_name FROM users WHERE status = 'active' LIMIT 5");
    $stmt->execute();
    $sponsors = $stmt->fetchAll();
    
    if (empty($sponsors)) {
        echo "<p style='color: red;'>❌ No active users found to use as sponsors!</p>";
        echo "<p>Creating sample users first...</p>";
        
        // Run sample users creation
        try {
            require_once 'create-sample-users.php';
            echo "<p style='color: green;'>✅ Sample users created!</p>";
            
            // Re-fetch sponsors
            $stmt->execute();
            $sponsors = $stmt->fetchAll();
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error creating sample users: " . $e->getMessage() . "</p>";
        }
    }
    
    if (!empty($sponsors)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>Username</th><th>Full Name</th></tr>";
        foreach ($sponsors as $sponsor) {
            echo "<tr>";
            echo "<td>{$sponsor['user_id']}</td>";
            echo "<td>{$sponsor['username']}</td>";
            echo "<td>{$sponsor['full_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test registration with sample data
        echo "<h2>Testing Registration Process</h2>";
        
        $testData = [
            'full_name' => 'Test User ' . date('His'),
            'email' => 'testuser' . date('His') . '@example.com',
            'phone' => '98765' . rand(10000, 99999),
            'password' => 'testpass123',
            'confirm_password' => 'testpass123',
            'sponsor_id' => $sponsors[0]['user_id'],
            'placement_side' => 'left',
            'address' => 'Test Address'
        ];
        
        echo "<h3>Test Data:</h3>";
        echo "<pre>" . print_r($testData, true) . "</pre>";
        
        // Validate the data
        $validator = new Validator($testData);
        $validator->required('full_name', 'Full name is required')
                 ->required('email', 'Email is required')
                 ->email('email')
                 ->required('phone', 'Phone number is required')
                 ->phone('phone')
                 ->required('password', 'Password is required')
                 ->minLength('password', 6)
                 ->required('confirm_password', 'Confirm password is required')
                 ->matches('confirm_password', 'password', 'Passwords do not match')
                 ->required('sponsor_id', 'Sponsor ID is required')
                 ->required('placement_side', 'Placement side is required');
        
        if ($validator->passes()) {
            echo "<p style='color: green;'>✅ Validation passed!</p>";
            
            // Test the registration process
            try {
                $db->beginTransaction();
                
                // Generate unique user ID and username
                $userId = generateUserId();
                $username = strtolower(str_replace(' ', '', $testData['full_name'])) . mt_rand(100, 999);
                
                // Check if username already exists and modify if needed
                $checkStmt = $db->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
                $checkStmt->execute([$username]);
                if ($checkStmt->fetchColumn() > 0) {
                    $username .= mt_rand(1000, 9999);
                }
                
                echo "<p>Generated User ID: <strong>$userId</strong></p>";
                echo "<p>Generated Username: <strong>$username</strong></p>";
                
                // Store password in plain text (as per your request)
                $plainPassword = $testData['password'];
                
                // Insert user
                $userStmt = $db->prepare("INSERT INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, placement_side, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $result = $userStmt->execute([
                    $userId,
                    $username,
                    $testData['email'],
                    $plainPassword,
                    $testData['full_name'],
                    $testData['phone'],
                    $testData['address'],
                    $testData['sponsor_id'],
                    $testData['placement_side'],
                    'active'
                ]);
                
                if ($result) {
                    echo "<p style='color: green;'>✅ User inserted successfully!</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to insert user!</p>";
                }
                
                // Create wallet for user
                $walletStmt = $db->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, 0.00, 0.00, 0.00)");
                $walletResult = $walletStmt->execute([$userId]);
                
                if ($walletResult) {
                    echo "<p style='color: green;'>✅ Wallet created successfully!</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to create wallet!</p>";
                }
                
                // Add to binary tree
                $binaryTree = new BinaryTree();
                $treeResult = $binaryTree->addUser($userId, $testData['sponsor_id'], $testData['placement_side']);
                
                if ($treeResult) {
                    echo "<p style='color: green;'>✅ Added to binary tree successfully!</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to add to binary tree!</p>";
                }
                
                $db->commit();
                echo "<p style='color: green;'><strong>✅ REGISTRATION SUCCESSFUL!</strong></p>";
                echo "<p>User ID: <strong>$userId</strong></p>";
                echo "<p>Username: <strong>$username</strong></p>";
                echo "<p>Password: <strong>$plainPassword</strong></p>";
                
            } catch (Exception $e) {
                if ($db->inTransaction()) {
                    $db->rollback();
                }
                echo "<p style='color: red;'>❌ Registration failed: " . $e->getMessage() . "</p>";
                echo "<p>Stack trace:</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Validation failed!</p>";
            echo "<p>Errors: " . $validator->getFirstError() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Fatal error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='user/register.php'>Try Manual Registration</a></p>";
echo "<p><a href='user/login.php'>Test Login</a></p>";
?>
