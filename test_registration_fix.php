<?php
/**
 * Test Registration Fix
 * Tests the fixed user registration functionality
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/functions.php';
require_once 'includes/Validator.php';
require_once 'includes/BinaryTree.php';

echo "<h1>Registration Fix Test</h1>";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. Database Connection Test</h2>";
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    echo "<h2>2. Check Available Sponsors</h2>";
    $sponsorStmt = $db->prepare("SELECT user_id, full_name FROM users WHERE status = 'active' LIMIT 5");
    $sponsorStmt->execute();
    $sponsors = $sponsorStmt->fetchAll();
    
    if (empty($sponsors)) {
        echo "<p style='color: red;'>❌ No sponsors found! Creating sample users...</p>";
        require_once 'run_sample_users.php';
        
        $sponsorStmt->execute();
        $sponsors = $sponsorStmt->fetchAll();
    }
    
    if (!empty($sponsors)) {
        echo "<p style='color: green;'>✅ Available sponsors:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>Full Name</th></tr>";
        foreach ($sponsors as $sponsor) {
            echo "<tr>";
            echo "<td>{$sponsor['user_id']}</td>";
            echo "<td>{$sponsor['full_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>3. Test Registration Process</h2>";
        
        $testData = [
            'full_name' => 'Test Fix User ' . date('His'),
            'email' => 'testfix' . date('His') . '@example.com',
            'phone' => '98765' . rand(10000, 99999),
            'password' => 'testpass123',
            'confirm_password' => 'testpass123',
            'sponsor_id' => $sponsors[0]['user_id'],
            'placement_side' => 'left',
            'address' => 'Test Address'
        ];
        
        echo "<p><strong>Test Data:</strong></p>";
        echo "<ul>";
        echo "<li>Name: {$testData['full_name']}</li>";
        echo "<li>Email: {$testData['email']}</li>";
        echo "<li>Phone: {$testData['phone']}</li>";
        echo "<li>Sponsor: {$testData['sponsor_id']}</li>";
        echo "<li>Placement: {$testData['placement_side']}</li>";
        echo "</ul>";
        
        // Validate the data
        $validator = new Validator($testData);
        $validator->required('full_name', 'Full name is required')
                 ->required('email', 'Email is required')
                 ->email('email')
                 ->required('phone', 'Phone number is required')
                 ->phone('phone')
                 ->required('password', 'Password is required')
                 ->minLength('password', 6)
                 ->required('confirm_password', 'Confirm password is required')
                 ->matches('confirm_password', 'password', 'Passwords do not match')
                 ->required('sponsor_id', 'Sponsor ID is required')
                 ->required('placement_side', 'Placement side is required');
        
        if ($validator->passes()) {
            echo "<p style='color: green;'>✅ Validation passed!</p>";
            
            try {
                echo "<p><strong>Starting registration process...</strong></p>";
                
                $db->beginTransaction();
                echo "<p>✅ Transaction started</p>";
                
                // Generate unique user ID and username
                $userId = generateUserId();
                $username = strtolower(str_replace(' ', '', $testData['full_name'])) . mt_rand(100, 999);
                
                // Check if username already exists and modify if needed
                $checkStmt = $db->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
                $checkStmt->execute([$username]);
                if ($checkStmt->fetchColumn() > 0) {
                    $username .= mt_rand(1000, 9999);
                }
                
                echo "<p>✅ Generated User ID: <strong>{$userId}</strong></p>";
                echo "<p>✅ Generated Username: <strong>{$username}</strong></p>";
                
                // Get default franchise ID
                $franchiseStmt = $db->prepare("SELECT id FROM franchise WHERE status = 'active' ORDER BY id LIMIT 1");
                $franchiseStmt->execute();
                $defaultFranchise = $franchiseStmt->fetch();
                $franchiseId = $defaultFranchise ? $defaultFranchise['id'] : null;
                
                echo "<p>✅ Franchise ID: <strong>" . ($franchiseId ?? 'NULL') . "</strong></p>";
                
                // Store password in plain text
                $plainPassword = $testData['password'];
                
                // Insert user
                $userStmt = $db->prepare("INSERT INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, franchise_id, placement_side, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $result = $userStmt->execute([
                    $userId,
                    $username,
                    $testData['email'],
                    $plainPassword,
                    $testData['full_name'],
                    $testData['phone'],
                    $testData['address'],
                    $testData['sponsor_id'],
                    $franchiseId,
                    $testData['placement_side'],
                    'active'
                ]);
                
                if ($result) {
                    echo "<p style='color: green;'>✅ User inserted successfully!</p>";
                } else {
                    throw new Exception("Failed to insert user");
                }
                
                // Create wallet for user
                $walletStmt = $db->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, 0.00, 0.00, 0.00)");
                $walletResult = $walletStmt->execute([$userId]);
                
                if ($walletResult) {
                    echo "<p style='color: green;'>✅ Wallet created successfully!</p>";
                } else {
                    throw new Exception("Failed to create wallet");
                }
                
                // Add to binary tree (without transaction since we're already in one)
                echo "<p><strong>Adding to binary tree...</strong></p>";
                $binaryTree = new BinaryTree();
                $treeResult = $binaryTree->addUser($userId, $testData['sponsor_id'], $testData['placement_side'], false);
                
                if ($treeResult) {
                    echo "<p style='color: green;'>✅ Added to binary tree successfully!</p>";
                } else {
                    throw new Exception("Failed to add user to binary tree");
                }
                
                $db->commit();
                echo "<p style='color: green;'><strong>✅ REGISTRATION COMPLETED SUCCESSFULLY!</strong></p>";
                echo "<p><strong>User Details:</strong></p>";
                echo "<ul>";
                echo "<li>User ID: <strong>{$userId}</strong></li>";
                echo "<li>Username: <strong>{$username}</strong></li>";
                echo "<li>Password: <strong>{$plainPassword}</strong></li>";
                echo "<li>Franchise ID: <strong>{$franchiseId}</strong></li>";
                echo "</ul>";
                
            } catch (Exception $e) {
                if ($db->inTransaction()) {
                    $db->rollback();
                    echo "<p style='color: orange;'>⚠️ Transaction rolled back</p>";
                }
                echo "<p style='color: red;'>❌ Registration failed: " . $e->getMessage() . "</p>";
                echo "<p><strong>Stack trace:</strong></p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Validation failed: " . $validator->getFirstError() . "</p>";
        }
    }
    
    echo "<h2>4. Test Product Assignment Fix</h2>";
    
    // Check if PVSystem can be instantiated without errors
    try {
        require_once 'includes/PVSystem.php';
        $pvSystem = new PVSystem();
        echo "<p style='color: green;'>✅ PVSystem class loaded successfully (BinaryTree dependency fixed)</p>";
        
        // Test if we can get PV totals (this would fail if BinaryTree wasn't included)
        if (isset($userId)) {
            $pvTotals = $pvSystem->getUserPVTotals($userId);
            echo "<p style='color: green;'>✅ PV totals retrieved successfully</p>";
            echo "<p>Left PV: {$pvTotals['left_pv']}, Right PV: {$pvTotals['right_pv']}, Total PV: {$pvTotals['total_pv']}</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ PVSystem error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Summary</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>✅ Fixes Applied:</h3>";
    echo "<ul>";
    echo "<li>✅ Fixed BinaryTree transaction conflict in user registration</li>";
    echo "<li>✅ Added BinaryTree include to PVSystem.php</li>";
    echo "<li>✅ Modified BinaryTree.addUser() to accept transaction parameter</li>";
    echo "<li>✅ Modified BinaryTree.addRoot() to accept transaction parameter</li>";
    echo "<li>✅ Updated user registration to use non-transactional BinaryTree calls</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Fatal error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='user/register.php' target='_blank'>Test User Registration Form</a></p>";
echo "<p><a href='franchise/login.php' target='_blank'>Test Franchise Login</a></p>";
echo "<p><a href='index.php'>Back to Home</a></p>";
?>
