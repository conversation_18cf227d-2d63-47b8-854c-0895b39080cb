<?php
/**
 * Test New PV System with Self PV and Upline Propagation
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'config/config.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/PVSystem.php';

try {
    $db = Database::getInstance();
    echo "<h2>Testing New PV System</h2>";
    
    // Test 1: Check database schema updates
    echo "<h3>1. Database Schema Check</h3>";
    
    // Check users table for new fields
    $userColumnsStmt = $db->query("SHOW COLUMNS FROM users WHERE Field IN ('self_pv', 'upline_pv')");
    $userColumns = $userColumnsStmt->fetchAll();
    
    if (count($userColumns) >= 2) {
        echo "✅ Users table has self_pv and upline_pv fields<br>";
        foreach ($userColumns as $column) {
            echo "- {$column['Field']}: {$column['Type']} (Default: {$column['Default']})<br>";
        }
    } else {
        echo "❌ Missing self_pv or upline_pv fields in users table<br>";
    }
    
    // Check pv_transactions table for 'self' side
    $enumStmt = $db->query("SHOW COLUMNS FROM pv_transactions WHERE Field = 'side'");
    $enumInfo = $enumStmt->fetch();
    
    if ($enumInfo && strpos($enumInfo['Type'], 'self') !== false) {
        echo "✅ PV transactions table supports 'self' side<br>";
        echo "- Side enum: {$enumInfo['Type']}<br>";
    } else {
        echo "❌ PV transactions table doesn't support 'self' side<br>";
    }
    
    // Test 2: Test PVSystem class
    echo "<h3>2. PVSystem Class Test</h3>";
    try {
        $pvSystem = new PVSystem();
        echo "✅ PVSystem instantiated successfully<br>";
        
        // Check if new methods exist
        if (method_exists($pvSystem, 'addSelfPV')) {
            echo "✅ addSelfPV method exists<br>";
        } else {
            echo "❌ addSelfPV method missing<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ PVSystem error: " . $e->getMessage() . "<br>";
    }
    
    // Test 3: Create test sponsor chain
    echo "<h3>3. Test Sponsor Chain</h3>";
    
    // Get some test users
    $usersStmt = $db->query("SELECT user_id, full_name, sponsor_id FROM users ORDER BY registration_date LIMIT 5");
    $users = $usersStmt->fetchAll();
    
    if (!empty($users)) {
        echo "✅ Found " . count($users) . " test users:<br>";
        foreach ($users as $user) {
            $sponsorInfo = $user['sponsor_id'] ? " (Sponsor: {$user['sponsor_id']})" : " (No sponsor)";
            echo "- {$user['user_id']}: {$user['full_name']}{$sponsorInfo}<br>";
        }
        
        // Test self PV addition
        $testUser = $users[0];
        echo "<h4>Testing Self PV Addition for {$testUser['user_id']}</h4>";
        
        // Get current PV totals
        $currentTotals = $pvSystem->getUserPVTotals($testUser['user_id']);
        echo "Current PV totals:<br>";
        echo "- Self PV: {$currentTotals['self_pv']}<br>";
        echo "- Upline PV: {$currentTotals['upline_pv']}<br>";
        echo "- Left PV: {$currentTotals['left_pv']}<br>";
        echo "- Right PV: {$currentTotals['right_pv']}<br>";
        echo "- Total PV: {$currentTotals['total_pv']}<br>";
        
        // Test adding self PV
        $testPV = 50.0;
        echo "<strong>Adding {$testPV} self PV...</strong><br>";
        
        $result = $pvSystem->addSelfPV(
            $testUser['user_id'],
            $testPV,
            'manual',
            null,
            'TEST_' . time(),
            'Test self PV addition with upline propagation',
            'admin',
            1
        );
        
        if ($result) {
            echo "✅ Self PV addition successful!<br>";
            
            // Check updated totals
            $newTotals = $pvSystem->getUserPVTotals($testUser['user_id']);
            echo "Updated PV totals:<br>";
            echo "- Self PV: {$newTotals['self_pv']} (+" . ($newTotals['self_pv'] - $currentTotals['self_pv']) . ")<br>";
            echo "- Upline PV: {$newTotals['upline_pv']}<br>";
            echo "- Total PV: {$newTotals['total_pv']} (+" . ($newTotals['total_pv'] - $currentTotals['total_pv']) . ")<br>";
            
            // Check if sponsor received upline PV
            if ($testUser['sponsor_id']) {
                echo "<h4>Checking Sponsor PV Propagation</h4>";
                $sponsorTotals = $pvSystem->getUserPVTotals($testUser['sponsor_id']);
                echo "Sponsor ({$testUser['sponsor_id']}) PV totals:<br>";
                echo "- Self PV: {$sponsorTotals['self_pv']}<br>";
                echo "- Upline PV: {$sponsorTotals['upline_pv']}<br>";
                echo "- Total PV: {$sponsorTotals['total_pv']}<br>";
                
                // Check recent upline transactions
                $uplineStmt = $db->prepare("SELECT * FROM pv_transactions WHERE user_id = ? AND side = 'upline' ORDER BY created_at DESC LIMIT 3");
                $uplineStmt->execute([$testUser['sponsor_id']]);
                $uplineTransactions = $uplineStmt->fetchAll();
                
                if (!empty($uplineTransactions)) {
                    echo "✅ Recent upline transactions for sponsor:<br>";
                    foreach ($uplineTransactions as $trans) {
                        echo "- {$trans['pv_amount']} PV from {$trans['description']} ({$trans['created_at']})<br>";
                    }
                } else {
                    echo "⚠️ No recent upline transactions found for sponsor<br>";
                }
            } else {
                echo "⚠️ Test user has no sponsor, cannot test upline propagation<br>";
            }
            
        } else {
            echo "❌ Self PV addition failed<br>";
        }
        
    } else {
        echo "❌ No test users found<br>";
    }
    
    // Test 4: Check recent transactions
    echo "<h3>4. Recent PV Transactions</h3>";
    $recentStmt = $db->query("
        SELECT pt.*, u.full_name 
        FROM pv_transactions pt 
        JOIN users u ON pt.user_id = u.user_id 
        WHERE pt.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY pt.created_at DESC 
        LIMIT 10
    ");
    $recentTransactions = $recentStmt->fetchAll();
    
    if (!empty($recentTransactions)) {
        echo "✅ Found " . count($recentTransactions) . " recent transactions:<br>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>User</th><th>Type</th><th>PV Amount</th><th>Side</th><th>Description</th><th>Date</th></tr>";
        foreach ($recentTransactions as $trans) {
            echo "<tr>";
            echo "<td>{$trans['full_name']} ({$trans['user_id']})</td>";
            echo "<td>{$trans['transaction_type']}</td>";
            echo "<td>{$trans['pv_amount']}</td>";
            echo "<td><strong>{$trans['side']}</strong></td>";
            echo "<td>" . htmlspecialchars($trans['description']) . "</td>";
            echo "<td>{$trans['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "⚠️ No recent transactions found<br>";
    }
    
    echo "<h3>5. System Summary</h3>";
    echo "<ul>";
    echo "<li>✅ Database schema updated for self PV and upline PV</li>";
    echo "<li>✅ PVSystem class enhanced with addSelfPV method</li>";
    echo "<li>✅ Upline propagation system implemented</li>";
    echo "<li>✅ Product assignment forms updated to use personal PV</li>";
    echo "<li>✅ Admin approval process updated</li>";
    echo "</ul>";
    
    echo "<h3>6. Next Steps</h3>";
    echo "<ol>";
    echo "<li>Test franchise product assignment with new system</li>";
    echo "<li>Test admin approval process</li>";
    echo "<li>Verify PV appears in user's personal balance</li>";
    echo "<li>Verify upline sponsors receive propagated PV</li>";
    echo "<li>Check billing and invoice generation</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h3>❌ Critical Error:</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
