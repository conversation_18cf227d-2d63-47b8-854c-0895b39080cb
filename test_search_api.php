<?php
/**
 * Test User Search API
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/functions.php';

echo "<h1>User Search API Test</h1>";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. Database Connection Test</h2>";
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    echo "<h2>2. Check Available Users</h2>";
    
    $usersStmt = $db->prepare("
        SELECT u.user_id, u.full_name, u.email, u.franchise_id, f.franchise_code
        FROM users u 
        LEFT JOIN franchise f ON u.franchise_id = f.id 
        WHERE u.status = 'active'
        ORDER BY u.created_at DESC 
        LIMIT 10
    ");
    $usersStmt->execute();
    $users = $usersStmt->fetchAll();
    
    if (!empty($users)) {
        echo "<p style='color: green;'>✅ Found " . count($users) . " active users:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>Name</th><th>Email</th><th>Franchise</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>{$user['full_name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>" . ($user['franchise_code'] ?? 'None') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No active users found!</p>";
    }
    
    echo "<h2>3. Check Franchise Authentication</h2>";
    
    $franchiseStmt = $db->prepare("SELECT id, franchise_code, username FROM franchise WHERE status = 'active' LIMIT 1");
    $franchiseStmt->execute();
    $franchise = $franchiseStmt->fetch();
    
    if ($franchise) {
        echo "<p style='color: green;'>✅ Active franchise found: {$franchise['franchise_code']}</p>";
        echo "<p><strong>Login credentials:</strong> {$franchise['username']} / franchise123</p>";
    } else {
        echo "<p style='color: red;'>❌ No active franchise found!</p>";
    }
    
    echo "<h2>4. Search API Test</h2>";
    
    if (!empty($users) && $franchise) {
        $testUser = $users[0];
        $searchQueries = [
            substr($testUser['full_name'], 0, 3),
            substr($testUser['email'], 0, 4),
            $testUser['user_id']
        ];
        
        echo "<p><strong>Test User:</strong> {$testUser['full_name']} ({$testUser['user_id']})</p>";
        echo "<p><strong>Search Queries to Test:</strong></p>";
        echo "<ul>";
        foreach ($searchQueries as $query) {
            echo "<li>'{$query}'</li>";
        }
        echo "</ul>";
        
        echo "<p><strong>API Endpoint:</strong> franchise/api/search_users.php</p>";
        echo "<p><strong>Note:</strong> The API requires franchise authentication. Test it through the franchise dashboard.</p>";
        
        echo "<h3>Manual Test Steps:</h3>";
        echo "<ol>";
        echo "<li><a href='franchise/login.php' target='_blank'>Login to franchise dashboard</a></li>";
        echo "<li><a href='franchise/products.php' target='_blank'>Go to product assignment page</a></li>";
        echo "<li>Click 'Assign Product' button</li>";
        echo "<li>Try searching in the user search field with the queries above</li>";
        echo "</ol>";
    }
    
    echo "<h2>5. Search Functionality Features</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Implemented Features:</h3>";
    echo "<ul>";
    echo "<li><strong>Real-time Search:</strong> 300ms debounce for smooth user experience</li>";
    echo "<li><strong>Multi-field Search:</strong> Search by name, email, or user ID</li>";
    echo "<li><strong>Franchise Restriction:</strong> Only shows users within the logged-in franchise</li>";
    echo "<li><strong>User-friendly Interface:</strong> Dropdown with user details</li>";
    echo "<li><strong>Selection Management:</strong> Clear selection and change options</li>";
    echo "<li><strong>Error Handling:</strong> Graceful handling of API errors</li>";
    echo "<li><strong>Security:</strong> Requires franchise authentication</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>6. API Response Format</h2>";
    echo "<p><strong>Example API Response:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo json_encode([
        'users' => [
            [
                'user_id' => 'SP202412345',
                'username' => 'testuser123',
                'full_name' => 'Test User',
                'email' => '<EMAIL>',
                'phone' => '9876543210',
                'display_name' => 'Test User (SP202412345)',
                'display_info' => '<EMAIL> | 9876543210'
            ]
        ]
    ], JSON_PRETTY_PRINT);
    echo "</pre>";
    
    echo "<h2>7. Integration Status</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>✅ Search Integration Complete!</h3>";
    echo "<p><strong>Files Modified/Created:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <code>franchise/api/search_users.php</code> - Search API endpoint</li>";
    echo "<li>✅ <code>franchise/products.php</code> - Updated with search functionality</li>";
    echo "<li>✅ JavaScript integration for real-time search</li>";
    echo "<li>✅ User selection and management interface</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='franchise/login.php' target='_blank'>Test Franchise Login</a></p>";
echo "<p><a href='franchise/products.php' target='_blank'>Test Product Assignment with Search</a></p>";
echo "<p><a href='test_complete_system.php'>Complete System Test</a></p>";
echo "<p><a href='index.php'>Back to Home</a></p>";
?>
