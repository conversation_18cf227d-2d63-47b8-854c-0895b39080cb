<?php
/**
 * Create Sample Product Images
 * This script creates actual image files for products
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

// Create uploads/products directory if it doesn't exist
$uploadDir = 'uploads/products/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

// Sample product images (base64 encoded 1x1 pixel images in different colors)
$sampleImages = [
    'PROD001' => [
        'data' => '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/wA==',
        'color' => 'green'
    ],
    'PROD002' => [
        'data' => '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/wA==',
        'color' => 'blue'
    ],
    'PROD003' => [
        'data' => '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/wA==',
        'color' => 'orange'
    ]
];

// Function to create a colored rectangle image
function createColoredImage($width, $height, $color, $filename) {
    // Create a simple SVG image
    $colors = [
        'green' => '#4CAF50',
        'blue' => '#2196F3',
        'orange' => '#FF9800',
        'red' => '#F44336',
        'purple' => '#9C27B0'
    ];
    
    $colorCode = $colors[$color] ?? '#4CAF50';
    
    $svg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="' . $width . '" height="' . $height . '" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="' . $colorCode . '"/>
    <rect x="10" y="10" width="' . ($width-20) . '" height="' . ($height-20) . '" fill="white" opacity="0.9"/>
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="18" fill="' . $colorCode . '" text-anchor="middle" dominant-baseline="middle" font-weight="bold">SAMPLE PRODUCT</text>
    <text x="50%" y="65%" font-family="Arial, sans-serif" font-size="12" fill="' . $colorCode . '" text-anchor="middle" dominant-baseline="middle">Click to view details</text>
</svg>';
    
    return file_put_contents($filename, $svg);
}

try {
    $db = Database::getInstance();
    
    // Get all products
    $stmt = $db->query("SELECT id, product_code, name FROM products WHERE status = 'active'");
    $products = $stmt->fetchAll();
    
    echo "Found " . count($products) . " products.\n";
    
    $colors = ['green', 'blue', 'orange', 'red', 'purple'];
    $colorIndex = 0;
    
    foreach ($products as $product) {
        $filename = $product['product_code'] . '_sample.svg';
        $filePath = $uploadDir . $filename;
        
        // Create colored image
        $color = $colors[$colorIndex % count($colors)];
        if (createColoredImage(400, 300, $color, $filePath)) {
            // Update product with image filename
            $updateStmt = $db->prepare("UPDATE products SET image = ? WHERE id = ?");
            $updateStmt->execute([$filename, $product['id']]);
            
            echo "Created image for product: " . $product['name'] . " (" . $filename . ") - Color: " . $color . "\n";
            $colorIndex++;
        } else {
            echo "Failed to create image for product: " . $product['name'] . "\n";
        }
    }
    
    echo "Sample images creation completed!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
