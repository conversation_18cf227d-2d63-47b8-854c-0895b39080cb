<?php
/**
 * Add Sample Product Images
 * This script creates placeholder images for existing products
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

// Create uploads/products directory if it doesn't exist
$uploadDir = 'uploads/products/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

// Function to download a sample image from a placeholder service
function createPlaceholderImage($width, $height, $text, $filename) {
    // Use a placeholder image service
    $url = "https://via.placeholder.com/{$width}x{$height}/f0f0f0/666666?text=" . urlencode($text);

    // Try to download the image
    $imageData = @file_get_contents($url);

    if ($imageData !== false) {
        $result = file_put_contents($filename, $imageData);
        return $result !== false ? $filename : false;
    }

    // Fallback: create a simple text file as placeholder
    $textContent = "Placeholder for: " . $text;
    $txtFilename = str_replace('.jpg', '.txt', $filename);
    $result = file_put_contents($txtFilename, $textContent);

    return $result !== false ? $txtFilename : false;
}

try {
    $db = Database::getInstance();
    
    // Get products without images
    $stmt = $db->query("SELECT id, product_code, name FROM products WHERE (image IS NULL OR image = '') AND status = 'active'");
    $products = $stmt->fetchAll();
    
    echo "Found " . count($products) . " products without images.\n";
    
    foreach ($products as $product) {
        $filename = $product['product_code'] . '_sample.jpg';
        $filePath = $uploadDir . $filename;

        // Create placeholder image
        $text = $product['product_code'];
        $imageFile = createPlaceholderImage(400, 300, $text, $filePath);
        if ($imageFile) {
            // Update product with image filename
            $imageFilename = basename($imageFile);
            $updateStmt = $db->prepare("UPDATE products SET image = ? WHERE id = ?");
            $updateStmt->execute([$imageFilename, $product['id']]);

            echo "Created image for product: " . $product['name'] . " (" . $imageFilename . ")\n";
        } else {
            echo "Failed to create image for product: " . $product['name'] . "\n";
        }
    }
    
    echo "Sample images creation completed!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
