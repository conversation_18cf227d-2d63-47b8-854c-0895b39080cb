<?php
/**
 * Create Sample Users Script
 * MLM Binary Plan System
 * This script creates sample admin, franchise, and user accounts
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/functions.php';
require_once 'includes/BinaryTree.php';

try {
    $db = Database::getInstance();
    $db->beginTransaction();
    
    echo "Creating sample users...\n";
    echo str_repeat('-', 50) . "\n";
    
    // 1. Create Admin User
    echo "1. Creating Admin User...\n";
    $adminPassword = password_hash('admin123', PASSWORD_BCRYPT);
    
    // Check if admin already exists
    $adminCheck = $db->prepare("SELECT COUNT(*) FROM admin WHERE username = 'admin'");
    $adminCheck->execute();
    
    if ($adminCheck->fetchColumn() == 0) {
        $adminStmt = $db->prepare("INSERT INTO admin (username, email, password, full_name, phone, status) VALUES (?, ?, ?, ?, ?, ?)");
        $adminStmt->execute([
            'admin',
            '<EMAIL>',
            $adminPassword,
            'System Administrator',
            '+91-**********',
            'active'
        ]);
        echo "   ✓ Admin created successfully\n";
        echo "   Username: admin\n";
        echo "   Password: admin123\n";
        echo "   Email: <EMAIL>\n";
    } else {
        echo "   ⚠ Admin already exists\n";
    }
    
    // 2. Create Franchise User
    echo "\n2. Creating Franchise User...\n";
    $franchisePassword = password_hash('franchise123', PASSWORD_BCRYPT);
    $franchiseCode = generateFranchiseCode();
    
    // Check if franchise already exists
    $franchiseCheck = $db->prepare("SELECT COUNT(*) FROM franchise WHERE username = 'franchise1'");
    $franchiseCheck->execute();
    
    if ($franchiseCheck->fetchColumn() == 0) {
        $franchiseStmt = $db->prepare("INSERT INTO franchise (franchise_code, username, email, password, full_name, phone, address, commission_rate, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $franchiseStmt->execute([
            $franchiseCode,
            'franchise1',
            '<EMAIL>',
            $franchisePassword,
            'Mumbai Franchise',
            '+91-9888888888',
            'Mumbai, Maharashtra, India',
            5.00,
            'active',
            1 // Created by admin
        ]);
        $franchiseId = $db->lastInsertId();
        echo "   ✓ Franchise created successfully\n";
        echo "   Franchise Code: {$franchiseCode}\n";
        echo "   Username: franchise1\n";
        echo "   Password: franchise123\n";
        echo "   Email: <EMAIL>\n";
    } else {
        echo "   ⚠ Franchise already exists\n";
        // Get existing franchise ID
        $franchiseStmt = $db->prepare("SELECT id FROM franchise WHERE username = 'franchise1'");
        $franchiseStmt->execute();
        $franchiseId = $franchiseStmt->fetchColumn();
    }
    
    // 3. Create Root User (Sponsor)
    echo "\n3. Creating Root User (Sponsor)...\n";
    $rootUserPassword = password_hash('user123', PASSWORD_BCRYPT);
    $rootUserId = generateUserId();
    
    // Check if root user already exists
    $rootUserCheck = $db->prepare("SELECT COUNT(*) FROM users WHERE username = 'rootuser'");
    $rootUserCheck->execute();
    
    if ($rootUserCheck->fetchColumn() == 0) {
        $rootUserStmt = $db->prepare("INSERT INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, franchise_id, placement_side, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $rootUserStmt->execute([
            $rootUserId,
            'rootuser',
            '<EMAIL>',
            $rootUserPassword,
            'Root User',
            '+91-9777777777',
            'Delhi, India',
            null, // No sponsor (root user)
            $franchiseId,
            null, // No placement side for root
            'active'
        ]);
        
        // Create wallet for root user
        $walletStmt = $db->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, 0.00, 0.00, 0.00)");
        $walletStmt->execute([$rootUserId]);
        
        // Add to binary tree as root
        $binaryTree = new BinaryTree();
        $binaryTree->addRoot($rootUserId);
        
        echo "   ✓ Root user created successfully\n";
        echo "   User ID: {$rootUserId}\n";
        echo "   Username: rootuser\n";
        echo "   Password: user123\n";
        echo "   Email: <EMAIL>\n";
    } else {
        echo "   ⚠ Root user already exists\n";
        // Get existing root user ID
        $rootUserStmt = $db->prepare("SELECT user_id FROM users WHERE username = 'rootuser'");
        $rootUserStmt->execute();
        $rootUserId = $rootUserStmt->fetchColumn();
    }
    
    // 4. Create Sample User (Under Root User)
    echo "\n4. Creating Sample User...\n";
    $sampleUserPassword = password_hash('user123', PASSWORD_BCRYPT);
    $sampleUserId = generateUserId();
    
    // Check if sample user already exists
    $sampleUserCheck = $db->prepare("SELECT COUNT(*) FROM users WHERE username = 'sampleuser'");
    $sampleUserCheck->execute();
    
    if ($sampleUserCheck->fetchColumn() == 0) {
        $sampleUserStmt = $db->prepare("INSERT INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, franchise_id, placement_side, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $sampleUserStmt->execute([
            $sampleUserId,
            'sampleuser',
            '<EMAIL>',
            $sampleUserPassword,
            'Sample User',
            '+91-9666666666',
            'Bangalore, India',
            $rootUserId, // Sponsored by root user
            $franchiseId,
            'left', // Placed on left side
            'active'
        ]);
        
        // Create wallet for sample user
        $walletStmt = $db->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, 0.00, 0.00, 0.00)");
        $walletStmt->execute([$sampleUserId]);
        
        // Add to binary tree
        $binaryTree = new BinaryTree();
        $binaryTree->addUser($sampleUserId, $rootUserId, 'left');
        
        echo "   ✓ Sample user created successfully\n";
        echo "   User ID: {$sampleUserId}\n";
        echo "   Username: sampleuser\n";
        echo "   Password: user123\n";
        echo "   Email: <EMAIL>\n";
        echo "   Sponsor: {$rootUserId} (Root User)\n";
        echo "   Placement: Left side\n";
    } else {
        echo "   ⚠ Sample user already exists\n";
    }
    
    // 5. Create Sample Products
    echo "\n5. Creating Sample Products...\n";
    $products = [
        [
            'product_code' => 'PROD001',
            'name' => 'Health Supplement A',
            'description' => 'Premium health supplement with natural ingredients',
            'price' => 1000.00,
            'pv_value' => 100.00
        ],
        [
            'product_code' => 'PROD002',
            'name' => 'Wellness Kit B',
            'description' => 'Complete wellness kit for daily health maintenance',
            'price' => 2500.00,
            'pv_value' => 250.00
        ],
        [
            'product_code' => 'PROD003',
            'name' => 'Energy Booster C',
            'description' => 'Natural energy booster for active lifestyle',
            'price' => 500.00,
            'pv_value' => 50.00
        ]
    ];
    
    foreach ($products as $product) {
        $productCheck = $db->prepare("SELECT COUNT(*) FROM products WHERE product_code = ?");
        $productCheck->execute([$product['product_code']]);
        
        if ($productCheck->fetchColumn() == 0) {
            $productStmt = $db->prepare("INSERT INTO products (product_code, name, description, price, pv_value, status, created_by) VALUES (?, ?, ?, ?, ?, 'active', 1)");
            $productStmt->execute([
                $product['product_code'],
                $product['name'],
                $product['description'],
                $product['price'],
                $product['pv_value']
            ]);
            echo "   ✓ Product created: {$product['name']}\n";
        }
    }
    
    $db->commit();
    
    echo "\n" . str_repeat('=', 50) . "\n";
    echo "✅ SAMPLE USERS CREATED SUCCESSFULLY!\n";
    echo str_repeat('=', 50) . "\n";
    
    echo "\n🔐 LOGIN CREDENTIALS:\n";
    echo str_repeat('-', 30) . "\n";
    
    echo "👨‍💼 ADMIN LOGIN:\n";
    echo "URL: /admin/login.php\n";
    echo "Username: admin\n";
    echo "Password: admin123\n\n";
    
    echo "🏪 FRANCHISE LOGIN:\n";
    echo "URL: /franchise/login.php\n";
    echo "Username: franchise1\n";
    echo "Password: franchise123\n\n";
    
    echo "👤 USER LOGINS:\n";
    echo "URL: /user/login.php\n";
    echo "Root User - Username: rootuser, Password: user123\n";
    echo "Sample User - Username: sampleuser, Password: user123\n\n";
    
    echo "🌐 WEBSITE:\n";
    echo "Main Page: /index.php\n";
    echo "Tree Viewer: /tree-viewer.php\n";
    echo "PV Manager: /pv-manager.php\n\n";
    
    echo "📝 NOTES:\n";
    echo "- All users are created with 'active' status\n";
    echo "- Sample user is placed under root user on left side\n";
    echo "- 3 sample products have been created\n";
    echo "- Wallets are created for all users\n";
    echo "- Binary tree structure is established\n";
    
} catch (Exception $e) {
    $db->rollback();
    echo "❌ Error creating sample users: " . $e->getMessage() . "\n";
    echo "Please check your database configuration and try again.\n";
}
?>
