<?php
/**
 * Fix Binary Tree Structure
 * This script fixes the binary tree to match the users table sponsor relationships
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/BinaryTree.php';

try {
    $db = Database::getInstance();
    $binaryTree = new BinaryTree();
    
    echo "Fixing Binary Tree Structure...\n";
    echo str_repeat("=", 40) . "\n";
    
    // Clear existing binary tree data
    echo "1. Clearing existing binary tree data...\n";
    $db->exec("DELETE FROM binary_tree");
    
    // Get users with their sponsor relationships
    $stmt = $db->query("SELECT user_id, sponsor_id, placement_side FROM users ORDER BY user_id");
    $users = $stmt->fetchAll();
    
    echo "2. Rebuilding binary tree from users table...\n";
    
    // First, add root users (users without sponsors)
    foreach ($users as $user) {
        if (!$user['sponsor_id']) {
            echo "   Adding root user: {$user['user_id']}\n";
            $binaryTree->addRoot($user['user_id']);
        }
    }
    
    // Then, add users with sponsors
    foreach ($users as $user) {
        if ($user['sponsor_id']) {
            $placementSide = $user['placement_side'] ?: 'left'; // Default to left if not specified
            echo "   Adding user: {$user['user_id']} under {$user['sponsor_id']} on {$placementSide} side\n";
            
            $result = $binaryTree->addUser($user['user_id'], $user['sponsor_id'], $placementSide);
            if (!$result) {
                echo "   ❌ Failed to add user {$user['user_id']}\n";
            } else {
                echo "   ✅ Successfully added user {$user['user_id']}\n";
            }
        }
    }
    
    echo "\n3. Verifying binary tree structure...\n";
    $stmt = $db->query('SELECT * FROM binary_tree ORDER BY level, user_id');
    $treeData = $stmt->fetchAll();
    
    foreach ($treeData as $node) {
        echo "   User: {$node['user_id']}, Parent: " . ($node['parent_id'] ?: 'ROOT') . ", Position: {$node['position']}, Level: {$node['level']}\n";
    }
    
    echo "\n✅ Binary tree structure fixed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error fixing binary tree: " . $e->getMessage() . "\n";
    exit(1);
}
?>
