<?php
/**
 * Update PV System Database Schema
 * This script updates the database schema to support the new PV propagation system
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "Starting PV system schema update...\n";
    
    // 1. Update pv_transactions table to support new transaction types and sides
    echo "1. Updating pv_transactions table...\n";
    
    // Add new transaction types
    $db->exec("ALTER TABLE pv_transactions MODIFY COLUMN transaction_type ENUM('purchase', 'bonus', 'manual', 'downline_bonus', 'upline_bonus') NOT NULL");
    echo "   - Added new transaction types: downline_bonus, upline_bonus\n";
    
    // Add new sides
    $db->exec("ALTER TABLE pv_transactions MODIFY COLUMN side ENUM('left', 'right', 'self', 'upline') NOT NULL");
    echo "   - Added new sides: self, upline\n";
    
    // 2. Add self_pv and upline_pv columns to users table if they don't exist
    echo "2. Updating users table...\n";
    
    // Check if self_pv column exists
    $result = $db->query("SHOW COLUMNS FROM users LIKE 'self_pv'");
    if ($result->rowCount() == 0) {
        $db->exec("ALTER TABLE users ADD COLUMN self_pv DECIMAL(10,2) DEFAULT 0.00 AFTER placement_side");
        echo "   - Added self_pv column\n";
    } else {
        echo "   - self_pv column already exists\n";
    }
    
    // Check if upline_pv column exists
    $result = $db->query("SHOW COLUMNS FROM users LIKE 'upline_pv'");
    if ($result->rowCount() == 0) {
        $db->exec("ALTER TABLE users ADD COLUMN upline_pv DECIMAL(10,2) DEFAULT 0.00 AFTER self_pv");
        echo "   - Added upline_pv column\n";
    } else {
        echo "   - upline_pv column already exists\n";
    }
    
    // 3. Create weekly_income_logs table for weekly processing
    echo "3. Creating weekly_income_logs table...\n";
    
    $weeklyIncomeTableSQL = "CREATE TABLE IF NOT EXISTS weekly_income_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id VARCHAR(20) NOT NULL,
        week_start_date DATE NOT NULL,
        week_end_date DATE NOT NULL,
        left_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        right_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        matched_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        income_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        weekly_capping_applied DECIMAL(10,2) DEFAULT 0.00,
        carry_forward_left DECIMAL(10,2) DEFAULT 0.00,
        carry_forward_right DECIMAL(10,2) DEFAULT 0.00,
        processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        INDEX idx_user_week (user_id, week_start_date),
        INDEX idx_week_start (week_start_date),
        UNIQUE KEY unique_user_week (user_id, week_start_date)
    )";
    
    $db->exec($weeklyIncomeTableSQL);
    echo "   - Created weekly_income_logs table\n";
    
    // 4. Create weekly_income_reports table for admin notifications
    echo "4. Creating weekly_income_reports table...\n";
    
    $weeklyReportsTableSQL = "CREATE TABLE IF NOT EXISTS weekly_income_reports (
        id INT PRIMARY KEY AUTO_INCREMENT,
        week_start_date DATE NOT NULL,
        week_end_date DATE NOT NULL,
        total_users_earned INT DEFAULT 0,
        total_income_distributed DECIMAL(12,2) DEFAULT 0.00,
        total_capping_applied DECIMAL(12,2) DEFAULT 0.00,
        report_generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        report_sent_at TIMESTAMP NULL,
        report_status ENUM('generated', 'sent', 'failed') DEFAULT 'generated',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_week_start (week_start_date),
        UNIQUE KEY unique_week (week_start_date)
    )";
    
    $db->exec($weeklyReportsTableSQL);
    echo "   - Created weekly_income_reports table\n";
    
    // 5. Update config table with weekly settings
    echo "5. Updating configuration...\n";
    
    // Change daily capping to weekly capping
    $db->exec("UPDATE config SET config_key = 'weekly_capping', description = 'Maximum weekly income per user in INR' WHERE config_key = 'daily_capping'");
    echo "   - Updated daily_capping to weekly_capping\n";
    
    // Add new config for weekly processing day
    $stmt = $db->prepare("INSERT IGNORE INTO config (config_key, config_value, description) VALUES (?, ?, ?)");
    $stmt->execute(['weekly_processing_day', '0', 'Day of week for weekly processing (0=Sunday, 1=Monday, etc.)']);
    echo "   - Added weekly_processing_day config\n";
    
    // Add admin notification email config
    $stmt->execute(['admin_notification_email', '<EMAIL>', 'Email address for weekly income reports']);
    echo "   - Added admin_notification_email config\n";
    
    echo "\nPV system schema update completed successfully!\n";
    echo "Summary of changes:\n";
    echo "- Updated pv_transactions table with new transaction types and sides\n";
    echo "- Added self_pv and upline_pv columns to users table\n";
    echo "- Created weekly_income_logs table for weekly processing\n";
    echo "- Created weekly_income_reports table for admin notifications\n";
    echo "- Updated configuration for weekly processing\n";
    
} catch (Exception $e) {
    echo "Error updating schema: " . $e->getMessage() . "\n";
    exit(1);
}
?>
