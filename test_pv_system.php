<?php
/**
 * Test PV System to Debug Issues
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'config/config.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/PVSystem.php';

try {
    $db = Database::getInstance();
    echo "<h2>Testing PV System</h2>";
    
    // Test 1: Check database tables
    echo "<h3>1. Database Table Check</h3>";
    $tables = ['pv_transactions', 'users', 'products', 'binary_tree'];
    foreach ($tables as $table) {
        $tableCheck = $db->query("SHOW TABLES LIKE '{$table}'")->fetch();
        if ($tableCheck) {
            echo "✅ {$table} table exists<br>";
        } else {
            echo "❌ {$table} table missing<br>";
        }
    }
    
    // Test 2: Check pv_transactions table structure
    echo "<h3>2. PV Transactions Table Structure</h3>";
    try {
        $columnsStmt = $db->query("DESCRIBE pv_transactions");
        $columns = $columnsStmt->fetchAll();
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "❌ Error checking table structure: " . $e->getMessage() . "<br>";
    }
    
    // Test 3: Check if we have test users
    echo "<h3>3. Test Users Check</h3>";
    $usersStmt = $db->query("SELECT user_id, full_name FROM users LIMIT 5");
    $users = $usersStmt->fetchAll();
    if (!empty($users)) {
        echo "✅ Found " . count($users) . " users:<br>";
        foreach ($users as $user) {
            echo "- " . $user['user_id'] . " (" . htmlspecialchars($user['full_name']) . ")<br>";
        }
    } else {
        echo "❌ No users found<br>";
    }
    
    // Test 4: Check if we have test products
    echo "<h3>4. Test Products Check</h3>";
    $productsStmt = $db->query("SELECT id, name, pv_value FROM products LIMIT 3");
    $products = $productsStmt->fetchAll();
    if (!empty($products)) {
        echo "✅ Found " . count($products) . " products:<br>";
        foreach ($products as $product) {
            echo "- ID: " . $product['id'] . ", Name: " . htmlspecialchars($product['name']) . ", PV: " . $product['pv_value'] . "<br>";
        }
    } else {
        echo "❌ No products found<br>";
    }
    
    // Test 5: Test PVSystem class instantiation
    echo "<h3>5. PVSystem Class Test</h3>";
    try {
        $pvSystem = new PVSystem();
        echo "✅ PVSystem class instantiated successfully<br>";
        
        // Test with a simple PV addition if we have users
        if (!empty($users)) {
            $testUser = $users[0]['user_id'];
            echo "<strong>Testing PV addition for user: {$testUser}</strong><br>";
            
            // Try to add a small amount of PV
            $result = $pvSystem->addPV(
                $testUser,
                10.0,
                'left',
                'manual',
                null,
                'TEST_' . time(),
                'Test PV addition for debugging',
                'admin',
                1
            );
            
            if ($result) {
                echo "✅ PV addition successful!<br>";
                
                // Check if the transaction was recorded
                $checkStmt = $db->prepare("SELECT * FROM pv_transactions WHERE user_id = ? AND description LIKE 'Test PV addition%' ORDER BY created_at DESC LIMIT 1");
                $checkStmt->execute([$testUser]);
                $transaction = $checkStmt->fetch();
                
                if ($transaction) {
                    echo "✅ PV transaction recorded in database:<br>";
                    echo "- Transaction ID: " . $transaction['id'] . "<br>";
                    echo "- PV Amount: " . $transaction['pv_amount'] . "<br>";
                    echo "- Side: " . $transaction['side'] . "<br>";
                    echo "- Type: " . $transaction['transaction_type'] . "<br>";
                } else {
                    echo "❌ PV transaction not found in database<br>";
                }
            } else {
                echo "❌ PV addition failed<br>";
                
                // Check error logs
                $errorLog = error_get_last();
                if ($errorLog) {
                    echo "Last error: " . $errorLog['message'] . "<br>";
                }
            }
        } else {
            echo "⚠️ Cannot test PV addition - no users available<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ PVSystem class error: " . $e->getMessage() . "<br>";
        echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // Test 6: Check BinaryTree class
    echo "<h3>6. BinaryTree Class Test</h3>";
    try {
        if (class_exists('BinaryTree')) {
            echo "✅ BinaryTree class available<br>";
            $binaryTree = new BinaryTree();
            echo "✅ BinaryTree instantiated successfully<br>";
        } else {
            echo "❌ BinaryTree class not found<br>";
        }
    } catch (Exception $e) {
        echo "❌ BinaryTree error: " . $e->getMessage() . "<br>";
    }
    
    // Test 7: Check Config class
    echo "<h3>7. Config Class Test</h3>";
    try {
        if (class_exists('Config')) {
            echo "✅ Config class available<br>";
            $config = Config::getInstance();
            echo "✅ Config instantiated successfully<br>";
            
            $pvRate = $config->get('pv_rate', 0.10);
            echo "✅ PV Rate: " . $pvRate . "<br>";
        } else {
            echo "❌ Config class not found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Config error: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>8. Debugging Summary</h3>";
    echo "<p>If PV addition failed, check the following:</p>";
    echo "<ul>";
    echo "<li>Ensure all required database tables exist</li>";
    echo "<li>Check that the user_id exists in the users table</li>";
    echo "<li>Verify that the side parameter is 'left' or 'right'</li>";
    echo "<li>Check database permissions</li>";
    echo "<li>Review error logs for detailed error messages</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3>❌ Critical Error:</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
