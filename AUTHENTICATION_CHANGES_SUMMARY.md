# Authentication System Changes Summary

## ⚠️ CRITICAL SECURITY WARNING ⚠️

**This document outlines changes made to convert the authentication system from secure bcrypt password hashing to PLAIN TEXT password storage. This is EXTREMELY DANGEROUS and violates all security standards.**

## Changes Made

### 1. User Registration (`user/register.php`)
- **BEFORE:** `$hashedPassword = password_hash($_POST['password'], PASSWORD_BCRYPT);`
- **AFTER:** `$hashedPassword = $_POST['password']; // PLAIN TEXT - SECURITY RISK!`
- **Impact:** New user passwords are now stored in plain text

### 2. User Login (`user/login.php`)
- **BEFORE:** `if ($user && password_verify($password, $user['password']))`
- **AFTER:** `if ($user && $password === $user['password']) // PLAIN TEXT COMPARISON`
- **Impact:** User login now compares plain text passwords

### 3. Franchise Login (`franchise/login.php`)
- **BEFORE:** `if ($franchise && password_verify($password, $franchise['password']))`
- **AFTER:** `if ($franchise && $password === $franchise['password']) // PLAIN TEXT COMPARISON`
- **Impact:** Franchise login now compares plain text passwords

### 4. Admin Login (`admin/login.php`)
- **BEFORE:** `if ($admin && password_verify($password, $admin['password']))`
- **AFTER:** `if ($admin && $password === $admin['password']) // PLAIN TEXT COMPARISON`
- **Impact:** Admin login now compares plain text passwords

### 5. Auth Class (`includes/Auth.php`)
- **BEFORE:** `if ($user && password_verify($credentials['password'], $user['password']))`
- **AFTER:** `if ($user && $credentials['password'] === $user['password']) // PLAIN TEXT COMPARISON`
- **Impact:** Central authentication method now uses plain text comparison

## Test Credentials

After running the password conversion script, the following credentials work:

### Admin Login (`/admin/login.php`)
- Username: `admin`
- Password: `admin123`

### Franchise Login (`/franchise/login.php`)
- Username: `franchise1`
- Password: `franchise123`

### User Login (`/user/login.php`)
- Username: `rootuser` | Password: `user123`
- Username: `sampleuser` | Password: `user123`

## Files Created for Testing/Debugging

1. `debug_auth.php` - Authentication debugging script
2. `test_authentication.php` - Comprehensive authentication testing
3. `convert_passwords_to_plaintext.php` - Converts existing hashed passwords to plain text
4. `run_sample_users.php` - Web interface to create sample users

## Backup Files Created

All original secure authentication files have been backed up in the `backups/` directory:
- `backups/README_SECURITY_WARNING.txt` - Security warning and restoration instructions
- `backups/user_register_backup.php` - Original secure registration

## Security Risks Introduced

1. **Password Exposure:** All passwords are now visible to anyone with database access
2. **Data Breach Impact:** If the database is compromised, all user passwords are immediately exposed
3. **Compliance Violations:** Violates GDPR, PCI DSS, and other security regulations
4. **Legal Liability:** Could result in legal action from users whose data is compromised
5. **Trust Loss:** Users will lose trust if they discover passwords are stored in plain text

## Recommendations

1. **IMMEDIATELY restore the secure authentication system** from backups
2. **Fix the original login bug** without compromising password security
3. **Force all users to reset their passwords** if plain text storage was used in production
4. **Implement proper security auditing** to prevent future security compromises

## Original Login Bug Analysis

The original "Invalid Credentials" error was likely caused by:
1. CSRF token verification issues
2. Database connection problems
3. Incorrect password verification logic
4. Session management issues

These issues should be debugged and fixed while maintaining secure password hashing.

---

**Date:** 2025-07-12  
**Reason:** User requested plain text password storage despite multiple security warnings  
**Status:** ⚠️ CRITICAL SECURITY VULNERABILITY INTRODUCED ⚠️
