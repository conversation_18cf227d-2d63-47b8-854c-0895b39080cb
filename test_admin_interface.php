<?php
/**
 * Test Admin Interface
 * This script tests the admin interface without complex transactions
 */

require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Testing Admin Interface</h2>";
    
    // 1. Check pending requests
    echo "<h3>1. Checking Pending Requests</h3>";
    
    $pendingStmt = $db->query("
        SELECT par.*, u.full_name as user_name, f.full_name as franchise_name, p.name as product_name, p.pv_value
        FROM product_assignment_requests par
        JOIN users u ON par.user_id = u.user_id
        JOIN franchise f ON par.franchise_id = f.id
        JOIN products p ON par.product_id = p.id
        WHERE par.status = 'pending'
        ORDER BY par.requested_at DESC
    ");
    $pendingRequests = $pendingStmt->fetchAll();
    
    if (!empty($pendingRequests)) {
        echo "<p style='color: green;'>✅ Found " . count($pendingRequests) . " pending request(s)</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Franchise</th><th>User</th><th>Product</th><th>Qty</th><th>PV Side</th><th>Total PV</th><th>Requested</th></tr>";
        foreach ($pendingRequests as $req) {
            $totalPV = $req['pv_value'] * $req['quantity'];
            echo "<tr>";
            echo "<td>{$req['id']}</td>";
            echo "<td>{$req['franchise_name']}</td>";
            echo "<td>{$req['user_name']}</td>";
            echo "<td>{$req['product_name']}</td>";
            echo "<td>{$req['quantity']}</td>";
            echo "<td>{$req['pv_side']}</td>";
            echo "<td>{$totalPV}</td>";
            echo "<td>{$req['requested_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No pending requests found</p>";
        
        // Create a test request
        echo "<h4>Creating Test Request</h4>";
        
        $franchiseStmt = $db->query("SELECT * FROM franchise WHERE status = 'active' LIMIT 1");
        $franchise = $franchiseStmt->fetch();
        
        $userStmt = $db->prepare("SELECT * FROM users WHERE franchise_id = ? AND status = 'active' LIMIT 1");
        $userStmt->execute([$franchise['id']]);
        $user = $userStmt->fetch();
        
        $productStmt = $db->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
        $product = $productStmt->fetch();
        
        if ($franchise && $user && $product) {
            $createStmt = $db->prepare("INSERT INTO product_assignment_requests (franchise_id, user_id, product_id, quantity, pv_side, description) VALUES (?, ?, ?, ?, ?, ?)");
            $createStmt->execute([
                $franchise['id'],
                $user['user_id'],
                $product['id'],
                1,
                'left',
                'Test request for admin interface testing'
            ]);
            
            echo "<p style='color: green;'>✅ Test request created! Request ID: " . $db->lastInsertId() . "</p>";
        }
    }
    
    // 2. Check approved/rejected requests
    echo "<h3>2. Recent Processed Requests</h3>";
    
    $processedStmt = $db->query("
        SELECT par.*, u.full_name as user_name, f.full_name as franchise_name, p.name as product_name, p.pv_value, a.full_name as admin_name
        FROM product_assignment_requests par
        JOIN users u ON par.user_id = u.user_id
        JOIN franchise f ON par.franchise_id = f.id
        JOIN products p ON par.product_id = p.id
        LEFT JOIN admin a ON par.processed_by = a.id
        WHERE par.status IN ('approved', 'rejected')
        ORDER BY par.processed_at DESC
        LIMIT 5
    ");
    $processedRequests = $processedStmt->fetchAll();
    
    if (!empty($processedRequests)) {
        echo "<p style='color: green;'>✅ Found " . count($processedRequests) . " processed request(s)</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Status</th><th>User</th><th>Product</th><th>Total PV</th><th>Admin</th><th>Processed</th></tr>";
        foreach ($processedRequests as $req) {
            $totalPV = $req['pv_value'] * $req['quantity'];
            $statusColor = $req['status'] === 'approved' ? 'green' : 'red';
            echo "<tr>";
            echo "<td>{$req['id']}</td>";
            echo "<td style='color: {$statusColor}; font-weight: bold;'>" . ucfirst($req['status']) . "</td>";
            echo "<td>{$req['user_name']}</td>";
            echo "<td>{$req['product_name']}</td>";
            echo "<td>{$totalPV}</td>";
            echo "<td>{$req['admin_name']}</td>";
            echo "<td>{$req['processed_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No processed requests found</p>";
    }
    
    // 3. Statistics
    echo "<h3>3. System Statistics</h3>";
    
    $statsStmt = $db->query("
        SELECT 
            COUNT(*) as total_requests,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests
        FROM product_assignment_requests
    ");
    $stats = $statsStmt->fetch();
    
    echo "<div style='display: flex; gap: 20px; margin: 10px 0;'>";
    echo "<div style='padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<strong>Total Requests:</strong> {$stats['total_requests']}";
    echo "</div>";
    echo "<div style='padding: 10px; border: 1px solid #ffc107; border-radius: 5px; background: #fff3cd;'>";
    echo "<strong>Pending:</strong> {$stats['pending_requests']}";
    echo "</div>";
    echo "<div style='padding: 10px; border: 1px solid #28a745; border-radius: 5px; background: #d4edda;'>";
    echo "<strong>Approved:</strong> {$stats['approved_requests']}";
    echo "</div>";
    echo "<div style='padding: 10px; border: 1px solid #dc3545; border-radius: 5px; background: #f8d7da;'>";
    echo "<strong>Rejected:</strong> {$stats['rejected_requests']}";
    echo "</div>";
    echo "</div>";
    
    // 4. Admin URLs
    echo "<h3>4. Admin Panel Access</h3>";
    
    $baseUrl = 'http://localhost/shaktipure/admin/';
    echo "<div style='margin: 10px 0;'>";
    echo "<p><strong>Login Credentials:</strong> admin / admin123</p>";
    echo "<ul>";
    echo "<li><a href='{$baseUrl}login.php' target='_blank' style='color: #007bff; text-decoration: none;'>🔐 Admin Login</a></li>";
    echo "<li><a href='{$baseUrl}product-approvals.php' target='_blank' style='color: #007bff; text-decoration: none;'>✅ Product Approvals</a></li>";
    echo "<li><a href='{$baseUrl}products.php' target='_blank' style='color: #007bff; text-decoration: none;'>📦 Product Management</a></li>";
    echo "<li><a href='{$baseUrl}franchises.php' target='_blank' style='color: #007bff; text-decoration: none;'>🏪 Franchise Management</a></li>";
    echo "</ul>";
    echo "</div>";
    
    // 5. Test Status
    echo "<h3>5. System Status</h3>";
    echo "<div style='padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
    echo "<p style='color: #155724; margin: 0;'><strong>✅ Product Assignment Approval System is Ready!</strong></p>";
    echo "<p style='color: #155724; margin: 5px 0 0 0;'>CSRF token issue has been fixed. The admin interface should now work properly.</p>";
    echo "</div>";
    
    echo "<h3>6. Testing Instructions</h3>";
    echo "<ol>";
    echo "<li>Login to the admin panel using the credentials above</li>";
    echo "<li>Go to 'Product Approvals' to see pending requests</li>";
    echo "<li>Click the green ✅ button to approve or red ❌ button to reject</li>";
    echo "<li>Add admin notes if needed and confirm the action</li>";
    echo "<li>Verify that the request status changes and PV is added to the user</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
