<?php
/**
 * ⚠️ CRITICAL SECURITY WARNING ⚠️
 * This script converts hashed passwords to plain text
 * This is EXTREMELY DANGEROUS and should NEVER be used in production!
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

echo "<h1>⚠️ Password Conversion to Plain Text ⚠️</h1>";
echo "<div style='background: #ffebee; border: 2px solid #f44336; padding: 15px; margin: 10px 0;'>";
echo "<h2 style='color: #d32f2f;'>CRITICAL SECURITY WARNING</h2>";
echo "<p><strong>This operation is EXTREMELY DANGEROUS and violates all security standards!</strong></p>";
echo "<p>Converting passwords to plain text exposes all user credentials and creates massive security vulnerabilities.</p>";
echo "</div>";

try {
    $db = Database::getInstance();
    
    // Known passwords for sample users (from create-sample-users.php)
    $knownPasswords = [
        'admin' => 'admin123',
        'franchise1' => 'franchise123', 
        'rootuser' => 'user123',
        'sampleuser' => 'user123'
    ];
    
    echo "<h2>Converting Admin Passwords</h2>";
    foreach ($knownPasswords as $username => $plainPassword) {
        if ($username === 'admin') {
            $stmt = $db->prepare("UPDATE admin SET password = ? WHERE username = ?");
            $stmt->execute([$plainPassword, $username]);
            if ($stmt->rowCount() > 0) {
                echo "<p>✓ Admin password converted: $username</p>";
            }
        }
    }
    
    echo "<h2>Converting Franchise Passwords</h2>";
    foreach ($knownPasswords as $username => $plainPassword) {
        if ($username === 'franchise1') {
            $stmt = $db->prepare("UPDATE franchise SET password = ? WHERE username = ?");
            $stmt->execute([$plainPassword, $username]);
            if ($stmt->rowCount() > 0) {
                echo "<p>✓ Franchise password converted: $username</p>";
            }
        }
    }
    
    echo "<h2>Converting User Passwords</h2>";
    foreach ($knownPasswords as $username => $plainPassword) {
        if (in_array($username, ['rootuser', 'sampleuser'])) {
            $stmt = $db->prepare("UPDATE users SET password = ? WHERE username = ?");
            $stmt->execute([$plainPassword, $username]);
            if ($stmt->rowCount() > 0) {
                echo "<p>✓ User password converted: $username</p>";
            }
        }
    }
    
    echo "<h2>Current Password Status</h2>";
    
    // Show admin passwords
    $stmt = $db->prepare("SELECT username, password FROM admin");
    $stmt->execute();
    $admins = $stmt->fetchAll();
    echo "<h3>Admin Accounts:</h3>";
    foreach ($admins as $admin) {
        echo "<p>Username: <strong>{$admin['username']}</strong> | Password: <strong>{$admin['password']}</strong></p>";
    }
    
    // Show franchise passwords  
    $stmt = $db->prepare("SELECT username, password FROM franchise");
    $stmt->execute();
    $franchises = $stmt->fetchAll();
    echo "<h3>Franchise Accounts:</h3>";
    foreach ($franchises as $franchise) {
        echo "<p>Username: <strong>{$franchise['username']}</strong> | Password: <strong>{$franchise['password']}</strong></p>";
    }
    
    // Show user passwords
    $stmt = $db->prepare("SELECT username, password FROM users");
    $stmt->execute();
    $users = $stmt->fetchAll();
    echo "<h3>User Accounts:</h3>";
    foreach ($users as $user) {
        echo "<p>Username: <strong>{$user['username']}</strong> | Password: <strong>{$user['password']}</strong></p>";
    }
    
    echo "<div style='background: #e8f5e8; border: 2px solid #4caf50; padding: 15px; margin: 20px 0;'>";
    echo "<h2 style='color: #2e7d32;'>✅ Password Conversion Complete</h2>";
    echo "<p><strong>All passwords are now stored in plain text format.</strong></p>";
    echo "<p>You can now test the login functionality with the following credentials:</p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> admin / admin123</li>";
    echo "<li><strong>Franchise:</strong> franchise1 / franchise123</li>";
    echo "<li><strong>Users:</strong> rootuser / user123, sampleuser / user123</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Test Login Functionality</a></p>";
echo "<p><a href='debug_auth.php'>Check Authentication Debug</a></p>";
?>
