<?php
/**
 * Test Product Approval Fix
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/Response.php';

try {
    $db = Database::getInstance();
    echo "<h2>Testing Product Approval Fix</h2>";
    
    // Check if we have any pending product assignment requests
    echo "<h3>1. Checking Product Assignment Requests</h3>";
    $requestsStmt = $db->query("SELECT COUNT(*) as count FROM product_assignment_requests WHERE status = 'pending'");
    $requestCount = $requestsStmt->fetch();
    echo "✅ Found " . $requestCount['count'] . " pending product assignment requests.<br>";
    
    if ($requestCount['count'] == 0) {
        echo "<p><strong>Note:</strong> No pending requests found. You need to create a product assignment request from a franchise account first.</p>";
        
        // Show how to create a test request
        echo "<h3>2. How to Test the Approval Process</h3>";
        echo "<ol>";
        echo "<li>Login to a franchise account (e.g., <EMAIL> / password123)</li>";
        echo "<li>Go to 'Product Assignment' section</li>";
        echo "<li>Select a user, product, quantity, and PV side</li>";
        echo "<li>Submit the assignment request</li>";
        echo "<li>Login to admin account (<EMAIL> / admin123)</li>";
        echo "<li>Go to 'Product Approvals' section</li>";
        echo "<li>Click the green ✅ button to approve the request</li>";
        echo "<li>Check that the success message appears and the request status changes</li>";
        echo "</ol>";
    } else {
        // Show the pending requests
        echo "<h3>2. Pending Requests Details</h3>";
        $detailsStmt = $db->query("
            SELECT par.*, u.full_name as user_name, f.full_name as franchise_name, p.name as product_name, p.pv_value
            FROM product_assignment_requests par
            JOIN users u ON par.user_id = u.user_id
            JOIN franchise f ON par.franchise_id = f.id
            JOIN products p ON par.product_id = p.id
            WHERE par.status = 'pending'
            ORDER BY par.requested_at DESC
            LIMIT 5
        ");
        $requests = $detailsStmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Franchise</th><th>User</th><th>Product</th><th>Quantity</th><th>PV Side</th><th>Total PV</th><th>Requested</th></tr>";
        foreach ($requests as $request) {
            $totalPV = $request['pv_value'] * $request['quantity'];
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['franchise_name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['user_name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['product_name']) . "</td>";
            echo "<td>" . $request['quantity'] . "</td>";
            echo "<td>" . ucfirst($request['pv_side']) . "</td>";
            echo "<td>" . $totalPV . " PV</td>";
            echo "<td>" . date('M d, Y H:i', strtotime($request['requested_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check database tables
    echo "<h3>3. Database Table Status</h3>";
    
    $tables = ['product_assignment_requests', 'purchase_orders', 'pv_transactions', 'products', 'users', 'franchise'];
    foreach ($tables as $table) {
        $tableCheck = $db->query("SHOW TABLES LIKE '{$table}'")->fetch();
        if ($tableCheck) {
            $countStmt = $db->query("SELECT COUNT(*) as count FROM {$table}");
            $count = $countStmt->fetch();
            echo "✅ {$table}: " . $count['count'] . " records<br>";
        } else {
            echo "❌ {$table}: Table not found<br>";
        }
    }
    
    // Test the approval workflow components
    echo "<h3>4. Testing Approval Workflow Components</h3>";
    
    // Test PVSystem class
    echo "<strong>PVSystem Class:</strong> ";
    if (class_exists('PVSystem')) {
        echo "✅ Available<br>";
    } else {
        echo "❌ Not found<br>";
    }
    
    // Test Response class
    echo "<strong>Response Class:</strong> ";
    if (class_exists('Response')) {
        echo "✅ Available<br>";
    } else {
        echo "❌ Not found<br>";
    }
    
    // Test flash message functions
    echo "<strong>Flash Message Functions:</strong> ";
    if (function_exists('setSuccessMessage') && function_exists('setErrorMessage')) {
        echo "✅ Available<br>";
    } else {
        echo "❌ Not found<br>";
    }
    
    echo "<h3>5. Fix Summary</h3>";
    echo "<p><strong>Issues Fixed:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Removed nested database transactions that were causing conflicts</li>";
    echo "<li>✅ Fixed flash message system to use setSuccessMessage() and setErrorMessage()</li>";
    echo "<li>✅ Added proper redirects after approval/rejection</li>";
    echo "<li>✅ Enhanced error handling and validation</li>";
    echo "</ul>";
    
    echo "<p><strong>The product approval process should now work correctly!</strong></p>";
    
    echo "<h3>6. Next Steps</h3>";
    echo "<ol>";
    echo "<li>Go to admin panel: <a href='admin/product-approvals.php' target='_blank'>Product Approvals</a></li>";
    echo "<li>Try approving a pending request</li>";
    echo "<li>Check that success message appears</li>";
    echo "<li>Verify that PV is added to user's account</li>";
    echo "<li>Check that purchase order is created</li>";
    echo "<li>Verify that user can see the product in their purchase history</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error occurred:</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
