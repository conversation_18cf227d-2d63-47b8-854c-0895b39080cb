<?php
require_once 'config/database.php';
require_once 'config/Connection.php';

$db = Database::getInstance();

echo "Binary Tree Data:\n";
$stmt = $db->query('SELECT * FROM binary_tree ORDER BY level, user_id');
$rows = $stmt->fetchAll();
foreach ($rows as $row) {
    echo 'ID: ' . $row['user_id'] . ', Parent: ' . ($row['parent_id'] ?: 'NULL') . ', Position: ' . $row['position'] . ', Level: ' . $row['level'] . "\n";
}

echo "\nUsers Table Data:\n";
$stmt2 = $db->query('SELECT user_id, sponsor_id, placement_side FROM users ORDER BY user_id');
$users = $stmt2->fetchAll();
foreach ($users as $user) {
    echo 'User: ' . $user['user_id'] . ', Sponsor: ' . ($user['sponsor_id'] ?: 'NULL') . ', Placement: ' . ($user['placement_side'] ?: 'NULL') . "\n";
}
?>
