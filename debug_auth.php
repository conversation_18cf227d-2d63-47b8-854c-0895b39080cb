<?php
/**
 * Authentication Debug Script
 * This script helps debug authentication issues
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/functions.php';

echo "<h1>Authentication Debug</h1>";

try {
    $db = Database::getInstance();
    
    // Check if users exist
    echo "<h2>Existing Users</h2>";
    $stmt = $db->prepare('SELECT user_id, username, email, password, status FROM users LIMIT 5');
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p style='color: red;'>No users found in database!</p>";
        echo "<p>You may need to run the sample users creation script first.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>Username</th><th>Email</th><th>Status</th><th>Password Hash (first 20 chars)</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['status']}</td>";
            echo "<td>" . substr($user['password'], 0, 20) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test password verification
        echo "<h2>Password Verification Test</h2>";
        $testUser = $users[0];
        echo "<p>Testing user: <strong>{$testUser['username']}</strong></p>";
        
        $testPasswords = ['user123', 'admin123', 'franchise123', 'password', '123456'];
        echo "<ul>";
        foreach ($testPasswords as $testPass) {
            $result = password_verify($testPass, $testUser['password']);
            $status = $result ? '<span style="color: green;">MATCH</span>' : '<span style="color: red;">NO MATCH</span>';
            echo "<li>Password '<strong>$testPass</strong>': $status</li>";
        }
        echo "</ul>";
    }
    
    // Check franchise users
    echo "<h2>Existing Franchise Users</h2>";
    $stmt = $db->prepare('SELECT id, franchise_code, username, email, password, status FROM franchise LIMIT 5');
    $stmt->execute();
    $franchises = $stmt->fetchAll();
    
    if (empty($franchises)) {
        echo "<p style='color: red;'>No franchise users found in database!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Franchise Code</th><th>Username</th><th>Email</th><th>Status</th><th>Password Hash (first 20 chars)</th></tr>";
        foreach ($franchises as $franchise) {
            echo "<tr>";
            echo "<td>{$franchise['id']}</td>";
            echo "<td>{$franchise['franchise_code']}</td>";
            echo "<td>{$franchise['username']}</td>";
            echo "<td>{$franchise['email']}</td>";
            echo "<td>{$franchise['status']}</td>";
            echo "<td>" . substr($franchise['password'], 0, 20) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check admin users
    echo "<h2>Existing Admin Users</h2>";
    $stmt = $db->prepare('SELECT id, username, email, password, status FROM admin LIMIT 5');
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "<p style='color: red;'>No admin users found in database!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Status</th><th>Password Hash (first 20 chars)</th></tr>";
        foreach ($admins as $admin) {
            echo "<tr>";
            echo "<td>{$admin['id']}</td>";
            echo "<td>{$admin['username']}</td>";
            echo "<td>{$admin['email']}</td>";
            echo "<td>{$admin['status']}</td>";
            echo "<td>" . substr($admin['password'], 0, 20) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test CSRF token functionality
    echo "<h2>CSRF Token Test</h2>";
    session_start();
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    echo "<p>CSRF Token: " . $_SESSION['csrf_token'] . "</p>";
    
    // Test database connection
    echo "<h2>Database Connection Test</h2>";
    echo "<p style='color: green;'>Database connection successful!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Back to Home</a></p>";
?>
