<?php
/**
 * Create Product Assignment Requests Table
 * This script creates the table for storing product assignment requests that require admin approval
 */

require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Creating Product Assignment Requests Table</h2>";
    
    // Create product_assignment_requests table
    $sql = "CREATE TABLE IF NOT EXISTS product_assignment_requests (
        id INT PRIMARY KEY AUTO_INCREMENT,
        franchise_id INT NOT NULL,
        user_id VARCHAR(20) NOT NULL,
        product_id INT NOT NULL,
        quantity INT NOT NULL DEFAULT 1,
        pv_side ENUM('left', 'right') NOT NULL,
        description TEXT,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed_at TIMESTAMP NULL,
        processed_by INT NULL,
        admin_notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (franchise_id) REFERENCES franchise(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (processed_by) REFERENCES admin(id),
        INDEX idx_franchise_status (franchise_id, status),
        INDEX idx_status_requested (status, requested_at),
        INDEX idx_user_product (user_id, product_id)
    )";
    
    $db->exec($sql);
    echo "<p style='color: green;'>✅ Product Assignment Requests table created successfully!</p>";
    
    // Check if table was created
    $checkTable = $db->query("SHOW TABLES LIKE 'product_assignment_requests'")->fetch();
    if ($checkTable) {
        echo "<p style='color: green;'>✅ Table verification successful!</p>";
        
        // Show table structure
        echo "<h3>Table Structure:</h3>";
        $columns = $db->query("DESCRIBE product_assignment_requests")->fetchAll();
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ Table verification failed!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error creating table: " . $e->getMessage() . "</p>";
}
?>
