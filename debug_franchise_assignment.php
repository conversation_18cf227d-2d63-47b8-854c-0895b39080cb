<?php
/**
 * Debug Franchise Assignment Issues
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/functions.php';

echo "<h1>Franchise Assignment Debug</h1>";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. Available Franchises</h2>";
    $franchiseStmt = $db->prepare("SELECT id, franchise_code, full_name, status FROM franchise");
    $franchiseStmt->execute();
    $franchises = $franchiseStmt->fetchAll();
    
    if (!empty($franchises)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Franchise Code</th><th>Full Name</th><th>Status</th></tr>";
        foreach ($franchises as $franchise) {
            echo "<tr>";
            echo "<td>{$franchise['id']}</td>";
            echo "<td>{$franchise['franchise_code']}</td>";
            echo "<td>{$franchise['full_name']}</td>";
            echo "<td>{$franchise['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No franchises found!</p>";
    }
    
    echo "<h2>2. Users and Their Franchise Assignment</h2>";
    $usersStmt = $db->prepare("
        SELECT u.user_id, u.full_name, u.franchise_id, f.franchise_code, f.full_name as franchise_name
        FROM users u 
        LEFT JOIN franchise f ON u.franchise_id = f.id 
        ORDER BY u.created_at DESC 
        LIMIT 10
    ");
    $usersStmt->execute();
    $users = $usersStmt->fetchAll();
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>User Name</th><th>Franchise ID</th><th>Franchise Code</th><th>Franchise Name</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>{$user['full_name']}</td>";
            echo "<td>" . ($user['franchise_id'] ?? 'NULL') . "</td>";
            echo "<td>" . ($user['franchise_code'] ?? 'NULL') . "</td>";
            echo "<td>" . ($user['franchise_name'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No users found!</p>";
    }
    
    echo "<h2>3. Users Without Franchise Assignment</h2>";
    $unassignedStmt = $db->prepare("SELECT user_id, full_name, email FROM users WHERE franchise_id IS NULL");
    $unassignedStmt->execute();
    $unassigned = $unassignedStmt->fetchAll();
    
    if (!empty($unassigned)) {
        echo "<p style='color: orange;'>⚠️ Found " . count($unassigned) . " users without franchise assignment:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>Full Name</th><th>Email</th></tr>";
        foreach ($unassigned as $user) {
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>{$user['full_name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Suggest fix
        if (!empty($franchises)) {
            echo "<h3>Suggested Fix:</h3>";
            echo "<p>Assign unassigned users to the first available franchise:</p>";
            echo "<form method='POST'>";
            echo "<input type='hidden' name='action' value='assign_default_franchise'>";
            echo "<p>Assign to franchise: ";
            echo "<select name='franchise_id'>";
            foreach ($franchises as $franchise) {
                echo "<option value='{$franchise['id']}'>{$franchise['franchise_code']} - {$franchise['full_name']}</option>";
            }
            echo "</select>";
            echo " <button type='submit'>Assign All</button></p>";
            echo "</form>";
        }
    } else {
        echo "<p style='color: green;'>✅ All users have franchise assignments!</p>";
    }
    
    // Handle franchise assignment
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'assign_default_franchise') {
        $franchiseId = (int) $_POST['franchise_id'];
        
        $updateStmt = $db->prepare("UPDATE users SET franchise_id = ? WHERE franchise_id IS NULL");
        $result = $updateStmt->execute([$franchiseId]);
        
        if ($result) {
            $affected = $updateStmt->rowCount();
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p style='color: #155724;'>✅ Successfully assigned {$affected} users to franchise ID {$franchiseId}!</p>";
            echo "</div>";
            
            // Refresh the page to show updated data
            echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
        } else {
            echo "<p style='color: red;'>❌ Failed to assign users to franchise!</p>";
        }
    }
    
    echo "<h2>4. Product Assignment Test</h2>";
    if (!empty($franchises) && !empty($users)) {
        $testFranchise = $franchises[0];
        $testUser = null;
        
        // Find a user assigned to this franchise
        foreach ($users as $user) {
            if ($user['franchise_id'] == $testFranchise['id']) {
                $testUser = $user;
                break;
            }
        }
        
        if ($testUser) {
            echo "<p style='color: green;'>✅ Found test user: {$testUser['full_name']} ({$testUser['user_id']}) under franchise {$testFranchise['franchise_code']}</p>";
            echo "<p><a href='franchise/login.php' target='_blank'>Login to franchise dashboard</a> to test product assignment</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ No users found under franchise {$testFranchise['franchise_code']} for testing</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>Back to Home</a></p>";
echo "<p><a href='test_franchise_system.php'>Franchise System Test</a></p>";
?>
