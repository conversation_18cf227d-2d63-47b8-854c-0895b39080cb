<?php
/**
 * Test CSRF Token Fix
 * This script tests if the CSRF token issue is resolved
 */

require_once 'includes/header.php';

echo "<h2>Testing CSRF Token Fix</h2>";

// Test CSRF token generation
echo "<h3>1. CSRF Token Generation Test</h3>";

if (function_exists('csrfTokenInput')) {
    echo "<p style='color: green;'>✅ csrfTokenInput() function exists</p>";
    $tokenInput = csrfTokenInput();
    echo "<p>Generated token input: <code>" . htmlspecialchars($tokenInput) . "</code></p>";
} else {
    echo "<p style='color: red;'>❌ csrfTokenInput() function not found</p>";
}

if (function_exists('verifyCsrfToken')) {
    echo "<p style='color: green;'>✅ verifyCsrfToken() function exists</p>";
} else {
    echo "<p style='color: red;'>❌ verifyCsrfToken() function not found</p>";
}

// Test session token
echo "<h3>2. Session Token Test</h3>";
if (isset($_SESSION['csrf_token'])) {
    echo "<p style='color: green;'>✅ CSRF token exists in session</p>";
    echo "<p>Session token: <code>" . htmlspecialchars($_SESSION['csrf_token']) . "</code></p>";
} else {
    echo "<p style='color: red;'>❌ No CSRF token in session</p>";
}

// Test form with CSRF token
echo "<h3>3. Test Form</h3>";
echo "<form method='POST' action='test_csrf_verification.php'>";
echo csrfTokenInput();
echo "<input type='hidden' name='test_action' value='csrf_test'>";
echo "<button type='submit' class='btn btn-primary'>Test CSRF Token</button>";
echo "</form>";

echo "<h3>4. Admin URLs to Test</h3>";
$baseUrl = 'http://localhost/shaktipure/admin/';
echo "<ul>";
echo "<li><a href='{$baseUrl}product-approvals.php' target='_blank'>Product Approvals</a></li>";
echo "<li><a href='{$baseUrl}products.php' target='_blank'>Product Management</a></li>";
echo "<li><a href='{$baseUrl}franchises.php' target='_blank'>Franchise Management</a></li>";
echo "</ul>";

echo "<p style='color: green;'>✅ CSRF token fix applied to all admin pages!</p>";
?>
