<?php
/**
 * Test Complete Approval Workflow
 * This script tests the entire approval process
 */

require_once 'config/Connection.php';
require_once 'includes/PVSystem.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Testing Complete Approval Workflow</h2>";
    
    // 1. Get a pending request
    echo "<h3>1. Finding Pending Request</h3>";
    
    $pendingStmt = $db->query("
        SELECT par.*, u.full_name as user_name, f.full_name as franchise_name, p.name as product_name, p.pv_value
        FROM product_assignment_requests par
        JOIN users u ON par.user_id = u.user_id
        JOIN franchise f ON par.franchise_id = f.id
        JOIN products p ON par.product_id = p.id
        WHERE par.status = 'pending'
        LIMIT 1
    ");
    $request = $pendingStmt->fetch();
    
    if ($request) {
        echo "<p style='color: green;'>✅ Found pending request:</p>";
        echo "<ul>";
        echo "<li><strong>Request ID:</strong> {$request['id']}</li>";
        echo "<li><strong>Franchise:</strong> {$request['franchise_name']}</li>";
        echo "<li><strong>User:</strong> {$request['user_name']} ({$request['user_id']})</li>";
        echo "<li><strong>Product:</strong> {$request['product_name']}</li>";
        echo "<li><strong>Quantity:</strong> {$request['quantity']}</li>";
        echo "<li><strong>PV Side:</strong> {$request['pv_side']}</li>";
        echo "<li><strong>Total PV:</strong> " . ($request['pv_value'] * $request['quantity']) . "</li>";
        echo "</ul>";
        
        // 2. Check user's current PV
        echo "<h3>2. User's Current PV</h3>";
        
        $pvStmt = $db->prepare("
            SELECT 
                SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv
            FROM pv_transactions 
            WHERE user_id = ?
        ");
        $pvStmt->execute([$request['user_id']]);
        $currentPV = $pvStmt->fetch();
        
        echo "<p><strong>Current PV:</strong></p>";
        echo "<ul>";
        echo "<li>Left PV: " . ($currentPV['left_pv'] ?? 0) . "</li>";
        echo "<li>Right PV: " . ($currentPV['right_pv'] ?? 0) . "</li>";
        echo "<li>Total PV: " . (($currentPV['left_pv'] ?? 0) + ($currentPV['right_pv'] ?? 0)) . "</li>";
        echo "</ul>";
        
        // 3. Simulate approval process
        echo "<h3>3. Simulating Approval Process</h3>";
        
        $adminId = 1; // Assuming admin ID 1 exists
        $totalPV = $request['pv_value'] * $request['quantity'];
        
        echo "<p><strong>Approval Details:</strong></p>";
        echo "<ul>";
        echo "<li>Admin ID: {$adminId}</li>";
        echo "<li>PV to Add: {$totalPV}</li>";
        echo "<li>Side: {$request['pv_side']}</li>";
        echo "<li>Admin Notes: Test approval via script</li>";
        echo "</ul>";
        
        try {
            // Add PV transaction (PVSystem handles its own transaction)
            $pvSystem = new PVSystem();
            $result = $pvSystem->addPV(
                $request['user_id'],
                $totalPV,
                $request['pv_side'],
                'manual',
                $request['product_id'],
                null,
                $request['description'] . " (Admin Approved via Test)",
                'admin',
                $adminId
            );

            if (!$result) {
                throw new Exception("Failed to add PV transaction");
            }

            // Update request status
            $updateStmt = $db->prepare("UPDATE product_assignment_requests SET status = 'approved', processed_at = NOW(), processed_by = ?, admin_notes = ? WHERE id = ?");
            $updateStmt->execute([$adminId, 'Test approval via script', $request['id']]);

            echo "<p style='color: green;'>✅ Approval process completed successfully!</p>";
            
            // 4. Verify the results
            echo "<h3>4. Verification</h3>";
            
            // Check updated PV
            $pvStmt->execute([$request['user_id']]);
            $newPV = $pvStmt->fetch();
            
            echo "<p><strong>Updated PV:</strong></p>";
            echo "<ul>";
            echo "<li>Left PV: " . ($newPV['left_pv'] ?? 0) . " (was: " . ($currentPV['left_pv'] ?? 0) . ")</li>";
            echo "<li>Right PV: " . ($newPV['right_pv'] ?? 0) . " (was: " . ($currentPV['right_pv'] ?? 0) . ")</li>";
            echo "<li>Total PV: " . (($newPV['left_pv'] ?? 0) + ($newPV['right_pv'] ?? 0)) . " (was: " . (($currentPV['left_pv'] ?? 0) + ($currentPV['right_pv'] ?? 0)) . ")</li>";
            echo "</ul>";
            
            // Check request status
            $checkStmt = $db->prepare("SELECT * FROM product_assignment_requests WHERE id = ?");
            $checkStmt->execute([$request['id']]);
            $updatedRequest = $checkStmt->fetch();
            
            echo "<p><strong>Request Status:</strong></p>";
            echo "<ul>";
            echo "<li>Status: {$updatedRequest['status']}</li>";
            echo "<li>Processed At: {$updatedRequest['processed_at']}</li>";
            echo "<li>Processed By: {$updatedRequest['processed_by']}</li>";
            echo "<li>Admin Notes: {$updatedRequest['admin_notes']}</li>";
            echo "</ul>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Approval failed: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ No pending requests found to test</p>";
        
        // Create a test request
        echo "<h3>Creating Test Request</h3>";
        
        $franchiseStmt = $db->query("SELECT * FROM franchise WHERE status = 'active' LIMIT 1");
        $franchise = $franchiseStmt->fetch();
        
        $userStmt = $db->prepare("SELECT * FROM users WHERE franchise_id = ? AND status = 'active' LIMIT 1");
        $userStmt->execute([$franchise['id']]);
        $user = $userStmt->fetch();
        
        $productStmt = $db->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
        $product = $productStmt->fetch();
        
        if ($franchise && $user && $product) {
            $createStmt = $db->prepare("INSERT INTO product_assignment_requests (franchise_id, user_id, product_id, quantity, pv_side, description) VALUES (?, ?, ?, ?, ?, ?)");
            $createStmt->execute([
                $franchise['id'],
                $user['user_id'],
                $product['id'],
                1,
                'left',
                'Test request created by workflow test'
            ]);
            
            echo "<p style='color: green;'>✅ Test request created! Request ID: " . $db->lastInsertId() . "</p>";
            echo "<p>You can now test the approval process in the admin panel.</p>";
        }
    }
    
    echo "<h3>5. Admin Panel Links</h3>";
    $baseUrl = 'http://localhost/shaktipure/admin/';
    echo "<ul>";
    echo "<li><a href='{$baseUrl}login.php' target='_blank'>Admin Login</a> (admin / admin123)</li>";
    echo "<li><a href='{$baseUrl}product-approvals.php' target='_blank'>Product Approvals</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
