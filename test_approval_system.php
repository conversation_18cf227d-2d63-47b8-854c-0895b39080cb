<?php
/**
 * Test Product Assignment Approval System
 * This script tests the complete approval workflow
 */

require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Testing Product Assignment Approval System</h2>";
    
    // 1. Check if required tables exist
    echo "<h3>1. Checking Database Tables</h3>";
    
    $tables = ['product_assignment_requests', 'franchise', 'users', 'products', 'admin'];
    foreach ($tables as $table) {
        $checkTable = $db->query("SHOW TABLES LIKE '$table'")->fetch();
        if ($checkTable) {
            echo "<p style='color: green;'>✅ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' missing</p>";
        }
    }
    
    // 2. Check if we have sample data
    echo "<h3>2. Checking Sample Data</h3>";
    
    // Check admin
    $adminStmt = $db->query("SELECT * FROM admin LIMIT 1");
    $admin = $adminStmt->fetch();
    if ($admin) {
        echo "<p style='color: green;'>✅ Admin found: {$admin['full_name']} ({$admin['username']})</p>";
    } else {
        echo "<p style='color: red;'>❌ No admin found</p>";
    }
    
    // Check franchise
    $franchiseStmt = $db->query("SELECT * FROM franchise WHERE status = 'active' LIMIT 1");
    $franchise = $franchiseStmt->fetch();
    if ($franchise) {
        echo "<p style='color: green;'>✅ Franchise found: {$franchise['full_name']} ({$franchise['franchise_code']})</p>";
    } else {
        echo "<p style='color: red;'>❌ No active franchise found</p>";
    }
    
    // Check user
    if ($franchise) {
        $userStmt = $db->prepare("SELECT * FROM users WHERE franchise_id = ? AND status = 'active' LIMIT 1");
        $userStmt->execute([$franchise['id']]);
        $user = $userStmt->fetch();
        if ($user) {
            echo "<p style='color: green;'>✅ User found: {$user['full_name']} ({$user['user_id']})</p>";
        } else {
            echo "<p style='color: red;'>❌ No active user found for franchise</p>";
        }
    }
    
    // Check product
    $productStmt = $db->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
    $product = $productStmt->fetch();
    if ($product) {
        echo "<p style='color: green;'>✅ Product found: {$product['name']} (PV: {$product['pv_value']})</p>";
    } else {
        echo "<p style='color: red;'>❌ No active product found</p>";
    }
    
    // 3. Test creating a product assignment request
    echo "<h3>3. Testing Product Assignment Request Creation</h3>";
    
    if ($franchise && $user && $product) {
        // Create a test request
        $requestStmt = $db->prepare("INSERT INTO product_assignment_requests (franchise_id, user_id, product_id, quantity, pv_side, description) VALUES (?, ?, ?, ?, ?, ?)");
        $result = $requestStmt->execute([
            $franchise['id'],
            $user['user_id'],
            $product['id'],
            2, // quantity
            'left', // pv_side
            'Test product assignment request'
        ]);
        
        if ($result) {
            $requestId = $db->lastInsertId();
            echo "<p style='color: green;'>✅ Product assignment request created successfully! Request ID: {$requestId}</p>";
            
            // Check the request
            $checkStmt = $db->prepare("
                SELECT par.*, u.full_name as user_name, f.full_name as franchise_name, p.name as product_name
                FROM product_assignment_requests par
                JOIN users u ON par.user_id = u.user_id
                JOIN franchise f ON par.franchise_id = f.id
                JOIN products p ON par.product_id = p.id
                WHERE par.id = ?
            ");
            $checkStmt->execute([$requestId]);
            $request = $checkStmt->fetch();
            
            if ($request) {
                echo "<p><strong>Request Details:</strong></p>";
                echo "<ul>";
                echo "<li>Franchise: {$request['franchise_name']}</li>";
                echo "<li>User: {$request['user_name']}</li>";
                echo "<li>Product: {$request['product_name']}</li>";
                echo "<li>Quantity: {$request['quantity']}</li>";
                echo "<li>PV Side: {$request['pv_side']}</li>";
                echo "<li>Status: {$request['status']}</li>";
                echo "<li>Requested At: {$request['requested_at']}</li>";
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>❌ Failed to create product assignment request</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Cannot test request creation - missing required data</p>";
    }
    
    // 4. Check pending requests
    echo "<h3>4. Checking Pending Requests</h3>";
    
    $pendingStmt = $db->query("
        SELECT par.*, u.full_name as user_name, f.full_name as franchise_name, p.name as product_name
        FROM product_assignment_requests par
        JOIN users u ON par.user_id = u.user_id
        JOIN franchise f ON par.franchise_id = f.id
        JOIN products p ON par.product_id = p.id
        WHERE par.status = 'pending'
        ORDER BY par.requested_at DESC
        LIMIT 5
    ");
    $pendingRequests = $pendingStmt->fetchAll();
    
    if (!empty($pendingRequests)) {
        echo "<p style='color: green;'>✅ Found " . count($pendingRequests) . " pending request(s)</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Franchise</th><th>User</th><th>Product</th><th>Quantity</th><th>PV Side</th><th>Requested</th></tr>";
        foreach ($pendingRequests as $req) {
            echo "<tr>";
            echo "<td>{$req['id']}</td>";
            echo "<td>{$req['franchise_name']}</td>";
            echo "<td>{$req['user_name']}</td>";
            echo "<td>{$req['product_name']}</td>";
            echo "<td>{$req['quantity']}</td>";
            echo "<td>{$req['pv_side']}</td>";
            echo "<td>{$req['requested_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No pending requests found</p>";
    }
    
    // 5. Test URLs
    echo "<h3>5. Testing Admin URLs</h3>";
    
    $baseUrl = 'http://localhost/shaktipure/admin/';
    $urls = [
        'dashboard.php' => 'Admin Dashboard',
        'products.php' => 'Product Management',
        'product-approvals.php' => 'Product Approvals',
        'franchises.php' => 'Franchise Management'
    ];
    
    foreach ($urls as $url => $title) {
        echo "<p><a href='{$baseUrl}{$url}' target='_blank'>{$title}</a> - {$baseUrl}{$url}</p>";
    }
    
    echo "<h3>6. System Status</h3>";
    echo "<p style='color: green;'>✅ Product Assignment Approval System is ready!</p>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Login to admin panel: <a href='{$baseUrl}login.php' target='_blank'>Admin Login</a></li>";
    echo "<li>Go to Product Approvals to see pending requests</li>";
    echo "<li>Test the approval/rejection workflow</li>";
    echo "<li>Check franchise dashboard to see the new request system</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
