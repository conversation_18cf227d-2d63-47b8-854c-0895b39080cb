<?php
/**
 * Test User Registration Script
 * Tests the registration functionality after fixing the inTransaction() error
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/functions.php';
require_once 'includes/Validator.php';
require_once 'includes/BinaryTree.php';

echo "<h1>User Registration Test</h1>";

try {
    $db = Database::getInstance();
    
    echo "<h2>Database Connection Test</h2>";
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    echo "<h2>inTransaction() Method Test</h2>";
    $hasMethod = method_exists($db, 'inTransaction');
    if ($hasMethod) {
        echo "<p style='color: green;'>✅ inTransaction() method exists</p>";
        
        // Test the method
        $inTransaction = $db->inTransaction();
        echo "<p>Current transaction status: " . ($inTransaction ? 'In transaction' : 'Not in transaction') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ inTransaction() method missing</p>";
    }
    
    echo "<h2>Transaction Test</h2>";
    try {
        $db->beginTransaction();
        echo "<p>✅ beginTransaction() works</p>";
        
        $inTransaction = $db->inTransaction();
        echo "<p>In transaction: " . ($inTransaction ? 'Yes' : 'No') . "</p>";
        
        $db->rollback();
        echo "<p>✅ rollback() works</p>";
        
        $inTransaction = $db->inTransaction();
        echo "<p>After rollback: " . ($inTransaction ? 'Still in transaction' : 'Not in transaction') . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Transaction test failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>Available Users for Sponsor</h2>";
    $stmt = $db->prepare("SELECT user_id, username, full_name FROM users WHERE status = 'active' LIMIT 5");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>Username</th><th>Full Name</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['full_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p>You can use any of these User IDs as sponsor when testing registration.</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ No users found. You may need to create sample users first.</p>";
        echo "<p><a href='run_sample_users.php'>Create Sample Users</a></p>";
    }
    
    echo "<h2>Registration Form Test</h2>";
    echo "<p><a href='user/register.php' target='_blank'>Test User Registration Form</a></p>";
    
    echo "<h2>Sample Registration Data</h2>";
    echo "<p>You can use this sample data for testing:</p>";
    echo "<ul>";
    echo "<li><strong>Full Name:</strong> Test User</li>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "<li><strong>Phone:</strong> 9876543210</li>";
    echo "<li><strong>Password:</strong> testpass123</li>";
    echo "<li><strong>Confirm Password:</strong> testpass123</li>";
    if (!empty($users)) {
        echo "<li><strong>Sponsor ID:</strong> {$users[0]['user_id']}</li>";
    }
    echo "<li><strong>Placement Side:</strong> left or right</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Back to Home</a></p>";
echo "<p><a href='debug_auth.php'>Authentication Debug</a></p>";
?>
