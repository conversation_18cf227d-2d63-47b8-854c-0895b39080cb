<?php
/**
 * File Upload Helper Class
 * MLM Binary Plan System
 */

class FileUpload {
    
    private $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    private $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    private $maxFileSize = 5242880; // 5MB
    private $uploadPath = 'uploads/';
    
    /**
     * Check if GD extension is available
     */
    private function isGDAvailable() {
        return extension_loaded('gd');
    }

    /**
     * Upload product image
     */
    public function uploadProductImage($file, $productCode = null) {
        try {
            // Validate file
            $validation = $this->validateFile($file);
            if (!$validation['success']) {
                return $validation;
            }

            // Create upload directory if it doesn't exist
            $uploadDir = $this->uploadPath . 'products/';
            if (!is_dir($uploadDir)) {
                if (!mkdir($uploadDir, 0755, true)) {
                    return ['success' => false, 'message' => 'Failed to create upload directory'];
                }
            }

            // Generate unique filename
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = ($productCode ? $productCode . '_' : '') . uniqid() . '.' . $extension;
            $filePath = $uploadDir . $filename;

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filePath)) {
                // Resize image if GD is available, otherwise skip resizing
                if ($this->isGDAvailable()) {
                    $resizeResult = $this->resizeImage($filePath, 800, 600);
                    if (!$resizeResult) {
                        // If resize fails, log warning but continue
                        error_log("Warning: Image resize failed for {$filePath}");
                    }
                } else {
                    error_log("Warning: GD extension not available, skipping image resize");
                }

                return [
                    'success' => true,
                    'filename' => $filename,
                    'path' => $filePath,
                    'url' => $this->uploadPath . 'products/' . $filename
                ];
            } else {
                return ['success' => false, 'message' => 'Failed to upload file'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Upload error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Validate uploaded file
     */
    private function validateFile($file) {
        // Check if file was uploaded
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'No file uploaded or upload error'];
        }
        
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            return ['success' => false, 'message' => 'File size exceeds maximum limit (5MB)'];
        }
        
        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $this->allowedTypes)) {
            return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed'];
        }
        
        // Check file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $this->allowedExtensions)) {
            return ['success' => false, 'message' => 'Invalid file extension'];
        }
        
        return ['success' => true];
    }
    
    /**
     * Resize image to fit within specified dimensions
     */
    private function resizeImage($filePath, $maxWidth, $maxHeight) {
        try {
            // Check if GD extension is available
            if (!$this->isGDAvailable()) {
                error_log("GD extension not available for image resizing");
                return false;
            }

            // Check if required GD functions exist
            $requiredFunctions = ['imagecreatefromjpeg', 'imagecreatefrompng', 'imagecreatefromgif', 'imagecreatetruecolor', 'imagecopyresampled'];
            foreach ($requiredFunctions as $function) {
                if (!function_exists($function)) {
                    error_log("Required GD function {$function} not available");
                    return false;
                }
            }

            $imageInfo = getimagesize($filePath);
            if (!$imageInfo) {
                return false;
            }

            $originalWidth = $imageInfo[0];
            $originalHeight = $imageInfo[1];
            $imageType = $imageInfo[2];

            // Calculate new dimensions
            $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);

            // If image is already smaller, don't resize
            if ($ratio >= 1) {
                return true;
            }

            $newWidth = round($originalWidth * $ratio);
            $newHeight = round($originalHeight * $ratio);

            // Create image resource based on type
            $sourceImage = false;
            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    if (function_exists('imagecreatefromjpeg')) {
                        $sourceImage = imagecreatefromjpeg($filePath);
                    }
                    break;
                case IMAGETYPE_PNG:
                    if (function_exists('imagecreatefrompng')) {
                        $sourceImage = imagecreatefrompng($filePath);
                    }
                    break;
                case IMAGETYPE_GIF:
                    if (function_exists('imagecreatefromgif')) {
                        $sourceImage = imagecreatefromgif($filePath);
                    }
                    break;
                case IMAGETYPE_WEBP:
                    if (function_exists('imagecreatefromwebp')) {
                        $sourceImage = imagecreatefromwebp($filePath);
                    }
                    break;
                default:
                    return false;
            }

            if (!$sourceImage) {
                error_log("Failed to create image resource from {$filePath}");
                return false;
            }

            // Create new image
            if (!function_exists('imagecreatetruecolor')) {
                error_log("imagecreatetruecolor function not available");
                return false;
            }

            $newImage = imagecreatetruecolor($newWidth, $newHeight);
            if (!$newImage) {
                error_log("Failed to create new image resource");
                imagedestroy($sourceImage);
                return false;
            }

            // Preserve transparency for PNG and GIF
            if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
                if (function_exists('imagealphablending') && function_exists('imagesavealpha')) {
                    imagealphablending($newImage, false);
                    imagesavealpha($newImage, true);
                    if (function_exists('imagecolorallocatealpha') && function_exists('imagefilledrectangle')) {
                        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
                        imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
                    }
                }
            }

            // Resize image
            if (!function_exists('imagecopyresampled')) {
                error_log("imagecopyresampled function not available");
                imagedestroy($sourceImage);
                imagedestroy($newImage);
                return false;
            }

            imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

            // Save resized image
            $saveResult = false;
            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    if (function_exists('imagejpeg')) {
                        $saveResult = imagejpeg($newImage, $filePath, 90);
                    }
                    break;
                case IMAGETYPE_PNG:
                    if (function_exists('imagepng')) {
                        $saveResult = imagepng($newImage, $filePath);
                    }
                    break;
                case IMAGETYPE_GIF:
                    if (function_exists('imagegif')) {
                        $saveResult = imagegif($newImage, $filePath);
                    }
                    break;
                case IMAGETYPE_WEBP:
                    if (function_exists('imagewebp')) {
                        $saveResult = imagewebp($newImage, $filePath, 90);
                    }
                    break;
            }

            // Clean up
            if (function_exists('imagedestroy')) {
                imagedestroy($sourceImage);
                imagedestroy($newImage);
            }

            if (!$saveResult) {
                error_log("Failed to save resized image to {$filePath}");
                return false;
            }

            return true;

        } catch (Exception $e) {
            error_log("Image resize error: " . $e->getMessage());
            return false;
        } catch (Error $e) {
            error_log("Image resize fatal error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete uploaded file
     */
    public function deleteFile($filename, $subfolder = 'products') {
        $filePath = $this->uploadPath . $subfolder . '/' . $filename;
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        return true; // File doesn't exist, consider it deleted
    }
    
    /**
     * Get file URL
     */
    public function getFileUrl($filename, $subfolder = 'products') {
        if (empty($filename)) {
            return null;
        }
        return $this->uploadPath . $subfolder . '/' . $filename;
    }
    
    /**
     * Check if file exists
     */
    public function fileExists($filename, $subfolder = 'products') {
        $filePath = $this->uploadPath . $subfolder . '/' . $filename;
        return file_exists($filePath);
    }
}
?>
