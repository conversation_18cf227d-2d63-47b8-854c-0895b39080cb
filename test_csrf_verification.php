<?php
/**
 * Test CSRF Token Verification
 * This script tests if CSRF verification works
 */

require_once 'includes/header.php';

echo "<h2>CSRF Token Verification Test</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>POST Request Received</h3>";
    
    echo "<p><strong>POST Data:</strong></p>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<p><strong>Session Token:</strong> " . ($_SESSION['csrf_token'] ?? 'Not set') . "</p>";
    echo "<p><strong>Posted Token:</strong> " . ($_POST['csrf_token'] ?? 'Not provided') . "</p>";
    
    try {
        verifyCsrfToken();
        echo "<p style='color: green;'>✅ CSRF token verification PASSED!</p>";
        echo "<p>The CSRF token fix is working correctly.</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ CSRF token verification FAILED: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>No POST request received. Please submit the form from the test page.</p>";
}

echo "<p><a href='test_csrf_fix.php'>← Back to CSRF Test</a></p>";
?>
