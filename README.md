# ShaktiPure MLM Binary Plan System

A complete PHP-based Multi-Level Marketing (MLM) Binary Plan website with advanced features.

## Features

### 🔐 Authentication System
- Separate login systems for Admin, Franchise, and Users
- Session management with timeout
- Password hashing using bcrypt
- Email and phone validation

### 🧬 MLM Binary Tree Logic
- Binary tree structure (Left/Right system)
- Sponsor ID based registration
- Visual binary tree representation
- Automatic placement system

### 💸 PV (Point Value) System
- 1 PV = ₹0.10 conversion rate
- Permanent PV history storage
- Matching PV logic with carry forward
- Daily capping: Max ₹130,000 per user
- Automatic income calculation

### 👨‍💼 Admin Panel
- Complete user management
- PV overview and analytics
- Product management with PV values
- Withdrawal approval system
- Franchise management

### 🏪 Franchise Panel
- User registration under franchise
- Product assignment with PV
- Commission tracking
- Limited admin access

### 💰 Wallet & Withdrawal System
- Digital wallet for each user
- Automatic income credit from PV matching
- Withdrawal requests (minimum ₹500)
- Admin approval workflow

### 🛍 Product System
- Products with PV values
- Razorpay payment integration
- Manual product assignment
- Purchase history tracking

## Installation

1. **Setup Database**
   ```bash
   php setup.php
   ```

2. **Configure Database**
   - Edit `config/database.php` with your database credentials
   - Update Razorpay keys for payment integration

3. **Default Admin Credentials**
   - Username: `admin`
   - Password: `admin123`
   - **Important**: Change password after first login

## Project Structure

```
shaktipure/
├── admin/                  # Admin panel files
├── franchise/              # Franchise panel files
├── user/                   # User dashboard files
├── config/                 # Configuration files
│   ├── database.php        # Database configuration
│   └── Connection.php      # Database connection class
├── includes/               # Common includes
│   ├── functions.php       # Common functions
│   └── session.php         # Session management
├── assets/                 # CSS, JS, Images
├── uploads/                # File uploads
├── setup.php              # Database setup script
├── index.php              # Main landing page
└── README.md              # This file
```

## Database Tables

- `admin` - Admin users
- `franchise` - Franchise users
- `users` - Regular users
- `binary_tree` - Binary tree structure
- `products` - Product catalog
- `pv_transactions` - PV transaction history
- `wallet` - User wallet balances
- `wallet_transactions` - Wallet transaction history
- `withdrawals` - Withdrawal requests
- `income_logs` - PV matching income logs
- `login_logs` - User login history
- `config` - System configuration
- `purchase_orders` - Product purchase orders

## Configuration

### Database Settings
- Host: localhost
- Database: shaktipure_mlm
- Default user: root (no password)

### MLM Settings
- PV Rate: ₹0.10 per PV
- Daily Capping: ₹130,000
- Minimum Withdrawal: ₹500

### Payment Integration
- Razorpay integration for product purchases
- Test mode enabled by default

## Security Features

- Password hashing with bcrypt
- Session timeout management
- CSRF token protection
- SQL injection prevention with PDO
- Input sanitization
- User activity logging

## Usage

1. **Admin Functions**
   - Create and manage franchises
   - Add products with PV values
   - Monitor user activities
   - Approve withdrawals
   - View system analytics

2. **Franchise Functions**
   - Register users under franchise
   - Assign products to users
   - View commission earnings
   - Manage franchise users

3. **User Functions**
   - View binary tree structure
   - Check PV status (Left/Right/Matching)
   - Monitor wallet balance
   - Request withdrawals
   - Purchase products
   - Share referral links

## Development

- PHP 7.4+ required
- MySQL 5.7+ required
- Bootstrap 5 for frontend
- Font Awesome for icons
- PDO for database operations

## Support

For technical support, contact: <EMAIL>
