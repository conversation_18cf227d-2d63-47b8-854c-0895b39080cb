<?php
/**
 * Authentication Testing Script
 * Tests all authentication systems with plain text passwords
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/functions.php';
require_once 'includes/session.php';

echo "<h1>Authentication System Test</h1>";

// Test credentials
$testCredentials = [
    'admin' => ['username' => 'admin', 'password' => 'admin123', 'type' => 'admin'],
    'franchise' => ['username' => 'franchise1', 'password' => 'franchise123', 'type' => 'franchise'],
    'user1' => ['username' => 'rootuser', 'password' => 'user123', 'type' => 'user'],
    'user2' => ['username' => 'sampleuser', 'password' => 'user123', 'type' => 'user']
];

try {
    $db = Database::getInstance();
    
    echo "<h2>Testing Authentication Logic</h2>";
    
    foreach ($testCredentials as $testName => $creds) {
        echo "<h3>Testing {$creds['type']}: {$creds['username']}</h3>";
        
        if ($creds['type'] === 'admin') {
            $stmt = $db->prepare("SELECT id, username, email, password, full_name, phone, status FROM admin WHERE username = ? OR email = ?");
            $stmt->execute([$creds['username'], $creds['username']]);
        } elseif ($creds['type'] === 'franchise') {
            $stmt = $db->prepare("SELECT id, franchise_code, username, email, password, full_name, phone, status FROM franchise WHERE username = ? OR email = ?");
            $stmt->execute([$creds['username'], $creds['username']]);
        } else {
            $stmt = $db->prepare("SELECT id, user_id, username, email, password, full_name, phone, status FROM users WHERE username = ? OR email = ? OR user_id = ?");
            $stmt->execute([$creds['username'], $creds['username'], $creds['username']]);
        }
        
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<p>✓ User found in database</p>";
            echo "<p>Status: {$user['status']}</p>";
            echo "<p>Stored password: <strong>{$user['password']}</strong></p>";
            echo "<p>Test password: <strong>{$creds['password']}</strong></p>";
            
            // Test plain text comparison
            if ($creds['password'] === $user['password']) {
                echo "<p style='color: green;'>✅ Password match - Login should work!</p>";
            } else {
                echo "<p style='color: red;'>❌ Password mismatch - Login will fail!</p>";
            }
            
            // Test status
            if ($user['status'] === 'active') {
                echo "<p style='color: green;'>✅ Account is active</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Account status: {$user['status']}</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ User not found in database!</p>";
        }
        
        echo "<hr>";
    }
    
    echo "<h2>Login Test Links</h2>";
    echo "<p><a href='admin/login.php' target='_blank'>Test Admin Login (admin / admin123)</a></p>";
    echo "<p><a href='franchise/login.php' target='_blank'>Test Franchise Login (franchise1 / franchise123)</a></p>";
    echo "<p><a href='user/login.php' target='_blank'>Test User Login (rootuser / user123)</a></p>";
    echo "<p><a href='user/login.php' target='_blank'>Test User Login (sampleuser / user123)</a></p>";
    
    echo "<h2>Registration Test</h2>";
    echo "<p><a href='user/register.php' target='_blank'>Test User Registration (will store plain text password)</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Back to Home</a></p>";
?>
