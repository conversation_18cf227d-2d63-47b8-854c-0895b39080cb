<?php
/**
 * BA<PERSON>KUP OF ORIGINAL user/register.php
 * Created before modifying authentication system
 * This file contains the original bcrypt password hashing implementation
 */

require_once '../includes/header.php';

// Redirect if already logged in
if (isLoggedIn('user')) {
    Response::redirect('dashboard.php');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verifyCsrfToken();
    
    // Validate input
    $validator = new Validator($_POST);
    $validator->required('full_name', 'Full name is required')
             ->required('email', 'Email is required')
             ->email('email')
             ->unique('email', 'users', 'email')
             ->required('phone', 'Phone number is required')
             ->phone('phone')
             ->unique('phone', 'users', 'phone')
             ->required('password', 'Password is required')
             ->minLength('password', 6)
             ->required('confirm_password', 'Confirm password is required')
             ->matches('confirm_password', 'password', 'Passwords do not match')
             ->required('sponsor_id', 'Sponsor ID is required')
             ->exists('sponsor_id', 'users', 'user_id', 'Invalid sponsor ID')
             ->required('placement_side', 'Placement side is required');
    
    if ($validator->passes()) {
        try {
            $db = Database::getInstance();
            $db->beginTransaction();
            
            // Generate unique user ID and username
            $userId = generateUserId();
            $username = strtolower(str_replace(' ', '', $_POST['full_name'])) . mt_rand(100, 999);
            
            // Check if username already exists and modify if needed
            $checkStmt = $db->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $checkStmt->execute([$username]);
            if ($checkStmt->fetchColumn() > 0) {
                $username .= mt_rand(1000, 9999);
            }
            
            // Hash password - ORIGINAL SECURE IMPLEMENTATION
            $hashedPassword = password_hash($_POST['password'], PASSWORD_BCRYPT);
            
            // Insert user
            $userStmt = $db->prepare("INSERT INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, placement_side, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $userStmt->execute([
                $userId,
                $username,
                $_POST['email'],
                $hashedPassword,
                $_POST['full_name'],
                $_POST['phone'],
                $_POST['address'] ?? '',
                $_POST['sponsor_id'],
                $_POST['placement_side'],
                'active'
            ]);
            
            // Create wallet for user
            $walletStmt = $db->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, 0.00, 0.00, 0.00)");
            $walletStmt->execute([$userId]);
            
            // Add to binary tree
            $binaryTree = new BinaryTree();
            $binaryTree->addUser($userId, $_POST['sponsor_id'], $_POST['placement_side']);
            
            $db->commit();
            
            $success = "Registration successful! Your User ID is: <strong>{$userId}</strong> and Username is: <strong>{$username}</strong>. Please save these credentials.";
            
        } catch (Exception $e) {
           if ($db->inTransaction()) {
    $db->rollback();
}
            $error = 'Registration failed. Please try again.';
            error_log("User registration error: " . $e->getMessage());
        }
    } else {
        $error = $validator->getFirstError();
    }
}
?>
