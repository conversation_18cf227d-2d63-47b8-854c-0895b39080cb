<?php
/**
 * Add image field to products table
 * MLM Binary Plan System
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Adding image field to products table...</h2>";
    
    // Check if image field already exists
    $checkStmt = $db->query("SHOW COLUMNS FROM products LIKE 'image'");
    if ($checkStmt->fetch()) {
        echo "<p style='color: orange;'>⚠️ Image field already exists in products table.</p>";
    } else {
        // Add image field
        $sql = "ALTER TABLE products ADD COLUMN image VARCHAR(255) NULL AFTER description";
        $db->exec($sql);
        echo "<p style='color: green;'>✅ Image field added to products table successfully.</p>";
    }
    
    // Create uploads directory if it doesn't exist
    $uploadsDir = 'uploads/products';
    if (!is_dir($uploadsDir)) {
        if (mkdir($uploadsDir, 0755, true)) {
            echo "<p style='color: green;'>✅ Created uploads/products directory.</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create uploads/products directory.</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ uploads/products directory already exists.</p>";
    }
    
    // Create .htaccess file for uploads directory security
    $htaccessContent = "# Prevent direct access to uploaded files\n";
    $htaccessContent .= "Options -Indexes\n";
    $htaccessContent .= "# Allow only image files\n";
    $htaccessContent .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n";
    $htaccessContent .= "    Order allow,deny\n";
    $htaccessContent .= "    Allow from all\n";
    $htaccessContent .= "</FilesMatch>\n";
    $htaccessContent .= "<FilesMatch \"\\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$\">\n";
    $htaccessContent .= "    Order deny,allow\n";
    $htaccessContent .= "    Deny from all\n";
    $htaccessContent .= "</FilesMatch>\n";
    
    $htaccessFile = $uploadsDir . '/.htaccess';
    if (!file_exists($htaccessFile)) {
        if (file_put_contents($htaccessFile, $htaccessContent)) {
            echo "<p style='color: green;'>✅ Created .htaccess file for uploads security.</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create .htaccess file.</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ .htaccess file already exists in uploads directory.</p>";
    }
    
    echo "<h3>Migration completed successfully!</h3>";
    echo "<p><a href='admin/products.php'>Go to Product Management</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
