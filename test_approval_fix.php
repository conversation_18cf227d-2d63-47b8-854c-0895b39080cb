<?php
/**
 * Test Product Approval Fix
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'config/config.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/PVSystem.php';

try {
    $db = Database::getInstance();
    echo "<h2>Testing Product Approval Fix</h2>";
    
    // Test 1: Check if we have pending requests
    echo "<h3>1. Checking Pending Product Assignment Requests</h3>";
    $requestsStmt = $db->query("
        SELECT par.*, u.full_name as user_name, f.full_name as franchise_name, p.name as product_name, p.pv_value
        FROM product_assignment_requests par
        JOIN users u ON par.user_id = u.user_id
        JOIN franchise f ON par.franchise_id = f.id
        JOIN products p ON par.product_id = p.id
        WHERE par.status = 'pending'
        ORDER BY par.requested_at DESC
        LIMIT 3
    ");
    $requests = $requestsStmt->fetchAll();
    
    if (empty($requests)) {
        echo "⚠️ No pending requests found. Creating a test request...<br>";
        
        // Get a test user and product
        $userStmt = $db->query("SELECT user_id FROM users WHERE franchise_id IS NOT NULL LIMIT 1");
        $user = $userStmt->fetch();
        
        $productStmt = $db->query("SELECT id, pv_value FROM products WHERE status = 'active' LIMIT 1");
        $product = $productStmt->fetch();
        
        $franchiseStmt = $db->query("SELECT id FROM franchise WHERE status = 'active' LIMIT 1");
        $franchise = $franchiseStmt->fetch();
        
        if ($user && $product && $franchise) {
            $insertStmt = $db->prepare("INSERT INTO product_assignment_requests (franchise_id, user_id, product_id, quantity, pv_side, description) VALUES (?, ?, ?, ?, ?, ?)");
            $insertStmt->execute([
                $franchise['id'],
                $user['user_id'],
                $product['id'],
                1,
                'left',
                'Test product assignment for debugging'
            ]);
            echo "✅ Test request created successfully!<br>";
            
            // Re-fetch requests
            $requests = $requestsStmt->fetchAll();
        } else {
            echo "❌ Cannot create test request - missing user, product, or franchise data<br>";
        }
    }
    
    if (!empty($requests)) {
        echo "✅ Found " . count($requests) . " pending requests:<br>";
        foreach ($requests as $request) {
            $totalPV = $request['pv_value'] * $request['quantity'];
            echo "- Request ID: {$request['id']}, User: {$request['user_name']}, Product: {$request['product_name']}, PV: {$totalPV}<br>";
        }
    }
    
    // Test 2: Test PVSystem directly
    echo "<h3>2. Testing PVSystem Directly</h3>";
    try {
        $pvSystem = new PVSystem();
        echo "✅ PVSystem instantiated successfully<br>";
        
        if (!empty($requests)) {
            $testRequest = $requests[0];
            $testUserId = $testRequest['user_id'];
            $testPV = $testRequest['pv_value'] * $testRequest['quantity'];
            $testSide = $testRequest['pv_side'];
            
            echo "<strong>Testing PV addition:</strong><br>";
            echo "- User ID: {$testUserId}<br>";
            echo "- PV Amount: {$testPV}<br>";
            echo "- Side: {$testSide}<br>";
            
            // Test simplified PV addition
            $result = $pvSystem->addPVSimple(
                $testUserId,
                $testPV,
                $testSide,
                'manual',
                $testRequest['product_id'],
                'TEST_' . time(),
                'Test PV addition for approval debugging',
                'admin',
                1
            );
            
            if ($result) {
                echo "✅ PV addition successful!<br>";
                
                // Verify the transaction was recorded
                $verifyStmt = $db->prepare("SELECT * FROM pv_transactions WHERE user_id = ? AND description LIKE '%approval debugging%' ORDER BY created_at DESC LIMIT 1");
                $verifyStmt->execute([$testUserId]);
                $transaction = $verifyStmt->fetch();
                
                if ($transaction) {
                    echo "✅ PV transaction verified in database:<br>";
                    echo "- Transaction ID: {$transaction['id']}<br>";
                    echo "- PV Amount: {$transaction['pv_amount']}<br>";
                    echo "- Side: {$transaction['side']}<br>";
                } else {
                    echo "❌ PV transaction not found in database<br>";
                }
            } else {
                echo "❌ PV addition failed<br>";
            }
        }
    } catch (Exception $e) {
        echo "❌ PVSystem error: " . $e->getMessage() . "<br>";
    }
    
    // Test 3: Simulate the approval process
    echo "<h3>3. Simulating Approval Process</h3>";
    if (!empty($requests)) {
        $testRequest = $requests[0];
        
        echo "<strong>Simulating approval for Request ID: {$testRequest['id']}</strong><br>";
        
        try {
            // Get product details
            $productStmt = $db->prepare("SELECT * FROM products WHERE id = ?");
            $productStmt->execute([$testRequest['product_id']]);
            $product = $productStmt->fetch();
            
            if ($product) {
                $totalPV = $product['pv_value'] * $testRequest['quantity'];
                $totalAmount = $product['price'] * $testRequest['quantity'];
                $orderId = 'TEST_' . date('Ymd') . '_' . time();
                
                echo "- Product: {$product['name']}<br>";
                echo "- Total PV: {$totalPV}<br>";
                echo "- Total Amount: {$totalAmount}<br>";
                echo "- Order ID: {$orderId}<br>";
                
                // Test purchase order creation
                $orderStmt = $db->prepare("INSERT INTO purchase_orders (order_id, user_id, product_id, quantity, total_amount, pv_amount, placement_side, payment_method, payment_status, order_status) VALUES (?, ?, ?, ?, ?, ?, ?, 'manual', 'completed', 'confirmed')");
                $orderResult = $orderStmt->execute([
                    $orderId,
                    $testRequest['user_id'],
                    $testRequest['product_id'],
                    $testRequest['quantity'],
                    $totalAmount,
                    $totalPV,
                    $testRequest['pv_side']
                ]);
                
                if ($orderResult) {
                    echo "✅ Purchase order created successfully<br>";
                    
                    // Test PV addition
                    $pvResult = $pvSystem->addPVSimple(
                        $testRequest['user_id'],
                        $totalPV,
                        $testRequest['pv_side'],
                        'purchase',
                        $testRequest['product_id'],
                        $orderId,
                        $testRequest['description'] . " (Test Approval - Order: {$orderId})",
                        'admin',
                        1
                    );
                    
                    if ($pvResult) {
                        echo "✅ PV transaction added successfully<br>";
                        echo "✅ <strong>Approval simulation completed successfully!</strong><br>";
                        
                        // Clean up test data
                        $db->prepare("DELETE FROM purchase_orders WHERE order_id = ?")->execute([$orderId]);
                        $db->prepare("DELETE FROM pv_transactions WHERE reference_id = ?")->execute([$orderId]);
                        echo "✅ Test data cleaned up<br>";
                        
                    } else {
                        echo "❌ PV transaction failed<br>";
                    }
                } else {
                    echo "❌ Purchase order creation failed<br>";
                }
            } else {
                echo "❌ Product not found<br>";
            }
        } catch (Exception $e) {
            echo "❌ Approval simulation error: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h3>4. Summary</h3>";
    echo "<p><strong>Fixes Applied:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Added parameter validation to PVSystem::addPV()</li>";
    echo "<li>✅ Improved error handling and logging</li>";
    echo "<li>✅ Created simplified PV addition method (addPVSimple)</li>";
    echo "<li>✅ Temporarily disabled PV matching to isolate issues</li>";
    echo "<li>✅ Added detailed error logging to approval process</li>";
    echo "</ul>";
    
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Try approving a product assignment request in the admin panel</li>";
    echo "<li>Check the error logs if it still fails</li>";
    echo "<li>Once basic approval works, we can re-enable PV matching</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h3>❌ Critical Error:</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
