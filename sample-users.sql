-- Sample Users Creation SQL
-- MLM Binary Plan System
-- Run this SQL script to create sample admin, franchise, and user accounts

-- 1. Create Admin User
INSERT IGNORE INTO admin (username, email, password, full_name, phone, status) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', '+91-**********', 'active');

-- 2. Create Franchise User
INSERT IGNORE INTO franchise (franchise_code, username, email, password, full_name, phone, address, commission_rate, status, created_by) VALUES 
('FR2024001', 'franchise1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mumbai Franchise', '+91-**********', 'Mumbai, Maharashtra, India', 5.00, 'active', 1);

-- 3. <PERSON>reate Root User (Main Sponsor)
INSERT IGNORE INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, franchise_id, placement_side, status, registration_date) VALUES 
('SP20240001', 'rootuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Root User', '+91-**********', 'Delhi, India', NULL, 1, NULL, 'active', NOW());

-- 4. Create Sample User (Under Root User)
INSERT IGNORE INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, franchise_id, placement_side, status, registration_date) VALUES 
('SP20240002', 'sampleuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sample User', '+91-9666666666', 'Bangalore, India', 'SP20240001', 1, 'left', 'active', NOW());

-- 5. Create Wallets for Users
INSERT IGNORE INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES 
('SP20240001', 0.00, 0.00, 0.00),
('SP20240002', 0.00, 0.00, 0.00);

-- 6. Create Binary Tree Structure
INSERT IGNORE INTO binary_tree (user_id, parent_id, level, position, left_child, right_child) VALUES 
('SP20240001', NULL, 0, 'root', 'SP20240002', NULL),
('SP20240002', 'SP20240001', 1, 'left', NULL, NULL);

-- 7. Create Sample Products
INSERT IGNORE INTO products (product_code, name, description, price, pv_value, status, created_by, created_at) VALUES 
('PROD001', 'Health Supplement A', 'Premium health supplement with natural ingredients', 1000.00, 100.00, 'active', 1, NOW()),
('PROD002', 'Wellness Kit B', 'Complete wellness kit for daily health maintenance', 2500.00, 250.00, 'active', 1, NOW()),
('PROD003', 'Energy Booster C', 'Natural energy booster for active lifestyle', 500.00, 50.00, 'active', 1, NOW());

-- 8. Add some sample PV transactions for demonstration
INSERT IGNORE INTO pv_transactions (user_id, transaction_type, pv_amount, side, description, created_by_type, created_by_id, created_at) VALUES 
('SP20240001', 'manual', 50.00, 'left', 'Initial PV for demo', 'admin', 1, NOW()),
('SP20240001', 'manual', 30.00, 'right', 'Initial PV for demo', 'admin', 1, NOW()),
('SP20240002', 'manual', 25.00, 'left', 'Initial PV for demo', 'admin', 1, NOW());

-- 9. Create default system configuration
INSERT IGNORE INTO config (config_key, config_value, description, updated_by) VALUES 
('pv_rate', '0.10', 'PV to INR conversion rate (1 PV = ₹0.10)', 1),
('daily_capping', '130000.00', 'Maximum daily income per user in INR', 1),
('min_withdrawal', '500.00', 'Minimum withdrawal amount in INR', 1),
('razorpay_mode', 'test', 'Razorpay mode: test or live', 1),
('company_name', 'ShaktiPure MLM', 'Company name', 1),
('support_email', '<EMAIL>', 'Support email address', 1),
('support_phone', '+91-**********', 'Support phone number', 1);

-- Display created accounts information
SELECT 'ADMIN ACCOUNT CREATED' as message;
SELECT username, email, 'admin123' as password, full_name FROM admin WHERE username = 'admin';

SELECT 'FRANCHISE ACCOUNT CREATED' as message;
SELECT franchise_code, username, email, 'franchise123' as password, full_name FROM franchise WHERE username = 'franchise1';

SELECT 'USER ACCOUNTS CREATED' as message;
SELECT user_id, username, email, 'user123' as password, full_name, sponsor_id FROM users WHERE username IN ('rootuser', 'sampleuser');

SELECT 'PRODUCTS CREATED' as message;
SELECT product_code, name, price, pv_value FROM products;

-- Show login credentials summary
SELECT '=== LOGIN CREDENTIALS ===' as info;
SELECT 'ADMIN LOGIN:' as type, 'admin' as username, 'admin123' as password, '/admin/login.php' as url
UNION ALL
SELECT 'FRANCHISE LOGIN:', 'franchise1', 'franchise123', '/franchise/login.php'
UNION ALL
SELECT 'ROOT USER LOGIN:', 'rootuser', 'user123', '/user/login.php'
UNION ALL
SELECT 'SAMPLE USER LOGIN:', 'sampleuser', 'user123', '/user/login.php';
