<?php
/**
 * Database Migration: Add Self PV Field
 * This script adds a self_pv field to the users table to track individual PV
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    echo "<h2>Adding Self PV Field to Users Table</h2>";
    
    // Check if self_pv field already exists
    $checkStmt = $db->query("SHOW COLUMNS FROM users LIKE 'self_pv'");
    $fieldExists = $checkStmt->fetch();
    
    if ($fieldExists) {
        echo "<p style='color: orange;'>⚠️ self_pv field already exists in users table.</p>";
    } else {
        // Add self_pv field to users table
        $alterSql = "ALTER TABLE users ADD COLUMN self_pv DECIMAL(12,2) DEFAULT 0.00 AFTER status";
        $db->exec($alterSql);
        echo "<p style='color: green;'>✅ self_pv field added to users table successfully!</p>";
    }
    
    // Check if upline_pv field exists (for propagated PV from downline)
    $checkUplineStmt = $db->query("SHOW COLUMNS FROM users LIKE 'upline_pv'");
    $uplineFieldExists = $checkUplineStmt->fetch();
    
    if ($uplineFieldExists) {
        echo "<p style='color: orange;'>⚠️ upline_pv field already exists in users table.</p>";
    } else {
        // Add upline_pv field to users table
        $alterUplineSql = "ALTER TABLE users ADD COLUMN upline_pv DECIMAL(12,2) DEFAULT 0.00 AFTER self_pv";
        $db->exec($alterUplineSql);
        echo "<p style='color: green;'>✅ upline_pv field added to users table successfully!</p>";
    }
    
    // Update pv_transactions table to support self PV
    echo "<h3>Updating PV Transactions Table</h3>";
    
    // Check if 'self' is already in the side enum
    $enumStmt = $db->query("SHOW COLUMNS FROM pv_transactions WHERE Field = 'side'");
    $enumInfo = $enumStmt->fetch();
    
    if ($enumInfo && strpos($enumInfo['Type'], 'self') === false) {
        // Modify the enum to include 'self'
        $alterEnumSql = "ALTER TABLE pv_transactions MODIFY COLUMN side ENUM('left', 'right', 'self') NOT NULL";
        $db->exec($alterEnumSql);
        echo "<p style='color: green;'>✅ Updated pv_transactions.side enum to include 'self'!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ pv_transactions.side enum already includes 'self'.</p>";
    }
    
    // Display current table structure
    echo "<h3>Updated Users Table Structure</h3>";
    $columnsStmt = $db->query("DESCRIBE users");
    $columns = $columnsStmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        $highlight = in_array($column['Field'], ['self_pv', 'upline_pv']) ? 'background-color: #ffffcc;' : '';
        echo "<tr style='{$highlight}'>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Updated PV Transactions Table Structure</h3>";
    $pvColumnsStmt = $db->query("DESCRIBE pv_transactions");
    $pvColumns = $pvColumnsStmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($pvColumns as $column) {
        $highlight = $column['Field'] === 'side' ? 'background-color: #ffffcc;' : '';
        echo "<tr style='{$highlight}'>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Migration Summary</h3>";
    echo "<ul>";
    echo "<li>✅ Added <strong>self_pv</strong> field to users table for individual PV tracking</li>";
    echo "<li>✅ Added <strong>upline_pv</strong> field to users table for propagated PV from downline</li>";
    echo "<li>✅ Updated pv_transactions.side enum to include 'self' option</li>";
    echo "<li>✅ Database schema is now ready for the new PV system</li>";
    echo "</ul>";
    
    echo "<h3>Next Steps</h3>";
    echo "<ol>";
    echo "<li>Update PVSystem class to handle self PV</li>";
    echo "<li>Modify product assignment forms to use self PV</li>";
    echo "<li>Implement upline PV propagation logic</li>";
    echo "<li>Update admin approval process</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error occurred:</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
